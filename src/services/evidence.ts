import crypto from 'crypto';
import fs from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import { EvidenceItem, ChainOfCustodyEntry, TransactionInfo, AddressInfo } from '../types';
import { logger } from '../utils/logger';

export interface EvidenceCategory {
  id: string;
  name: string;
  description: string;
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  tags: string[];
}

export interface EvidenceIntegrityCheck {
  evidenceId: string;
  originalHash: string;
  currentHash: string;
  isValid: boolean;
  timestamp: string;
  verifiedBy: string;
}

export interface DigitalSignature {
  algorithm: string;
  signature: string;
  publicKey: string;
  timestamp: string;
  signedBy: string;
}

export interface EnhancedEvidenceItem extends EvidenceItem {
  category: EvidenceCategory;
  digitalSignature?: DigitalSignature;
  integrityChecks: EvidenceIntegrityCheck[];
  relatedEvidence: string[]; // IDs of related evidence items
  metadata: {
    source: string;
    collectionMethod: string;
    jurisdiction: string;
    legalBasis: string;
    retentionPeriod: string;
    accessLevel: 'PUBLIC' | 'RESTRICTED' | 'CONFIDENTIAL' | 'SECRET';
  };
  attachments: {
    id: string;
    filename: string;
    mimeType: string;
    size: number;
    hash: string;
  }[];
}

export class EnhancedEvidenceService {
  private evidenceItems: Map<string, EnhancedEvidenceItem> = new Map();
  private categories: Map<string, EvidenceCategory> = new Map();
  private investigationId: string;
  private outputDirectory: string;

  constructor(investigationId: string, outputDirectory: string) {
    this.investigationId = investigationId;
    this.outputDirectory = outputDirectory;
    this.initializeDefaultCategories();
  }

  private initializeDefaultCategories(): void {
    const defaultCategories: EvidenceCategory[] = [
      {
        id: 'transaction-evidence',
        name: 'Transaction Evidence',
        description: 'Bitcoin transaction data and related blockchain evidence',
        priority: 'HIGH',
        tags: ['blockchain', 'transaction', 'financial'],
      },
      {
        id: 'address-evidence',
        name: 'Address Evidence',
        description: 'Bitcoin address information and activity patterns',
        priority: 'MEDIUM',
        tags: ['blockchain', 'address', 'identity'],
      },
      {
        id: 'pattern-evidence',
        name: 'Pattern Evidence',
        description: 'Suspicious activity patterns and behavioral analysis',
        priority: 'HIGH',
        tags: ['analysis', 'pattern', 'suspicious'],
      },
      {
        id: 'risk-evidence',
        name: 'Risk Assessment Evidence',
        description: 'Risk scoring and threat assessment data',
        priority: 'CRITICAL',
        tags: ['risk', 'assessment', 'threat'],
      },
      {
        id: 'technical-evidence',
        name: 'Technical Evidence',
        description: 'Technical analysis and forensic artifacts',
        priority: 'MEDIUM',
        tags: ['technical', 'forensic', 'analysis'],
      },
    ];

    defaultCategories.forEach(category => {
      this.categories.set(category.id, category);
    });
  }

  /**
   * Create enhanced evidence item with comprehensive metadata
   */
  createEvidenceItem(
    evidenceType: string,
    description: string,
    data: Record<string, any>,
    categoryId: string = 'technical-evidence',
    metadata: Partial<EnhancedEvidenceItem['metadata']> = {}
  ): EnhancedEvidenceItem {
    const evidenceId = uuidv4();
    const timestamp = new Date().toISOString();

    // Create hash of the evidence data for integrity
    const evidenceHash = this.createDataHash(data);

    // Get category
    const category = this.categories.get(categoryId) || this.categories.get('technical-evidence')!;

    // Initialize chain of custody
    const chainOfCustody: ChainOfCustodyEntry[] = [
      {
        timestamp,
        action: 'evidence_created',
        userId: 'system',
        description: `Evidence item created: ${description}`,
      },
    ];

    // Create enhanced evidence item
    const evidenceItem: EnhancedEvidenceItem = {
      evidenceId,
      investigationId: this.investigationId,
      evidenceType: evidenceType as any,
      description,
      data,
      timestamp,
      hashValue: evidenceHash,
      chainOfCustody,
      category,
      integrityChecks: [
        {
          evidenceId,
          originalHash: evidenceHash,
          currentHash: evidenceHash,
          isValid: true,
          timestamp,
          verifiedBy: 'system',
        },
      ],
      relatedEvidence: [],
      metadata: {
        source: 'Bitcoin Forensic Investigation Tool v3.0',
        collectionMethod: 'automated_blockchain_analysis',
        jurisdiction: 'international',
        legalBasis: 'forensic_investigation',
        retentionPeriod: '7_years',
        accessLevel: 'RESTRICTED',
        ...metadata,
      },
      attachments: [],
    };

    this.evidenceItems.set(evidenceId, evidenceItem);

    logger.info('Enhanced evidence item created', {
      evidenceId,
      category: category.name,
      type: evidenceType,
      description,
    });

    return evidenceItem;
  }

  /**
   * Update chain of custody with detailed tracking
   */
  updateChainOfCustody(
    evidenceId: string,
    action: string,
    description: string,
    userId: string = 'system',
    additionalData?: Record<string, any>
  ): boolean {
    const evidence = this.evidenceItems.get(evidenceId);
    if (!evidence) {
      logger.error('Evidence item not found for chain of custody update', { evidenceId });
      return false;
    }

    const custodyEntry: ChainOfCustodyEntry = {
      timestamp: new Date().toISOString(),
      action,
      userId,
      description,
    };

    // Add digital signature if available
    if (additionalData?.signature) {
      custodyEntry.signature = additionalData.signature;
    }

    evidence.chainOfCustody.push(custodyEntry);

    logger.info('Chain of custody updated', {
      evidenceId,
      action,
      userId,
      description,
    });

    return true;
  }

  /**
   * Verify evidence integrity
   */
  verifyEvidenceIntegrity(
    evidenceId: string,
    verifiedBy: string = 'system'
  ): EvidenceIntegrityCheck {
    const evidence = this.evidenceItems.get(evidenceId);
    if (!evidence) {
      throw new Error(`Evidence item not found: ${evidenceId}`);
    }

    const currentHash = this.createDataHash(evidence.data);
    const isValid = currentHash === evidence.hashValue;

    const integrityCheck: EvidenceIntegrityCheck = {
      evidenceId,
      originalHash: evidence.hashValue,
      currentHash,
      isValid,
      timestamp: new Date().toISOString(),
      verifiedBy,
    };

    evidence.integrityChecks.push(integrityCheck);

    if (!isValid) {
      logger.error('Evidence integrity check failed', {
        evidenceId,
        originalHash: evidence.hashValue,
        currentHash,
      });

      this.updateChainOfCustody(
        evidenceId,
        'integrity_check_failed',
        'Evidence integrity verification failed - data may have been tampered with',
        verifiedBy
      );
    } else {
      logger.info('Evidence integrity verified', { evidenceId });

      this.updateChainOfCustody(
        evidenceId,
        'integrity_verified',
        'Evidence integrity successfully verified',
        verifiedBy
      );
    }

    return integrityCheck;
  }

  /**
   * Link related evidence items
   */
  linkEvidence(evidenceId1: string, evidenceId2: string, relationship: string): boolean {
    const evidence1 = this.evidenceItems.get(evidenceId1);
    const evidence2 = this.evidenceItems.get(evidenceId2);

    if (!evidence1 || !evidence2) {
      logger.error('Cannot link evidence - one or both items not found', {
        evidenceId1,
        evidenceId2,
      });
      return false;
    }

    // Add bidirectional links
    if (!evidence1.relatedEvidence.includes(evidenceId2)) {
      evidence1.relatedEvidence.push(evidenceId2);
    }

    if (!evidence2.relatedEvidence.includes(evidenceId1)) {
      evidence2.relatedEvidence.push(evidenceId1);
    }

    // Update chain of custody for both items
    const description = `Linked to evidence ${evidenceId2} (${relationship})`;
    this.updateChainOfCustody(evidenceId1, 'evidence_linked', description);
    this.updateChainOfCustody(
      evidenceId2,
      'evidence_linked',
      `Linked to evidence ${evidenceId1} (${relationship})`
    );

    logger.info('Evidence items linked', {
      evidenceId1,
      evidenceId2,
      relationship,
    });

    return true;
  }

  private createDataHash(data: Record<string, any>): string {
    return crypto
      .createHash('sha256')
      .update(JSON.stringify(data, Object.keys(data).sort()))
      .digest('hex');
  }

  /**
   * Add attachment to evidence item
   */
  addAttachment(
    evidenceId: string,
    filename: string,
    content: Buffer,
    mimeType: string = 'application/octet-stream'
  ): boolean {
    const evidence = this.evidenceItems.get(evidenceId);
    if (!evidence) {
      logger.error('Evidence item not found for attachment', { evidenceId });
      return false;
    }

    const attachmentId = uuidv4();
    const attachmentHash = crypto.createHash('sha256').update(content).digest('hex');

    // Save attachment to file system
    const attachmentPath = path.join(
      this.outputDirectory,
      'attachments',
      `${attachmentId}_${filename}`
    );

    try {
      fs.mkdirSync(path.dirname(attachmentPath), { recursive: true });
      fs.writeFileSync(attachmentPath, content);

      evidence.attachments.push({
        id: attachmentId,
        filename,
        mimeType,
        size: content.length,
        hash: attachmentHash,
      });

      this.updateChainOfCustody(
        evidenceId,
        'attachment_added',
        `Attachment added: ${filename} (${content.length} bytes)`,
        'system'
      );

      logger.info('Attachment added to evidence', {
        evidenceId,
        attachmentId,
        filename,
        size: content.length,
      });

      return true;
    } catch (error: any) {
      logger.error('Failed to save attachment', {
        evidenceId,
        filename,
        error: error.message,
      });
      return false;
    }
  }

  /**
   * Create evidence from transaction data
   */
  createTransactionEvidence(transaction: TransactionInfo): EnhancedEvidenceItem {
    return this.createEvidenceItem(
      'transaction',
      `Bitcoin transaction ${transaction.txid}`,
      {
        txid: transaction.txid,
        fromAddress: transaction.fromAddress,
        toAddress: transaction.toAddress,
        amountBtc: transaction.amountBtc,
        depth: transaction.depth,
        timestamp: transaction.timestamp,
        blockHeight: transaction.blockHeight,
        confirmations: transaction.confirmations,
        fees: transaction.fees,
        inputCount: transaction.inputCount,
        outputCount: transaction.outputCount,
      },
      'transaction-evidence',
      {
        source: 'blockchain_api',
        collectionMethod: 'automated_transaction_trace',
        legalBasis: 'financial_crime_investigation',
      }
    );
  }

  /**
   * Create evidence from address data
   */
  createAddressEvidence(address: AddressInfo): EnhancedEvidenceItem {
    return this.createEvidenceItem(
      'address',
      `Bitcoin address ${address.address}`,
      {
        address: address.address,
        addressType: address.addressType,
        totalReceived: address.totalReceived,
        totalSent: address.totalSent,
        balance: address.balance,
        transactionCount: address.transactionCount,
        firstSeen: address.firstSeen,
        lastSeen: address.lastSeen,
        riskScore: address.riskScore,
        tags: address.tags,
      },
      'address-evidence',
      {
        source: 'blockchain_api',
        collectionMethod: 'automated_address_analysis',
        legalBasis: 'financial_crime_investigation',
      }
    );
  }

  /**
   * Export comprehensive evidence package
   */
  async exportEvidencePackage(): Promise<string> {
    const packageData = {
      metadata: {
        investigationId: this.investigationId,
        exportTimestamp: new Date().toISOString(),
        toolVersion: '3.0.0',
        evidenceCount: this.evidenceItems.size,
        categoryCount: this.categories.size,
      },
      categories: Array.from(this.categories.values()),
      evidence: Array.from(this.evidenceItems.values()),
      integrity: {
        packageHash: this.createPackageHash(),
        verificationInstructions: 'Verify package integrity by recalculating hash of evidence data',
      },
      legalNotice: {
        disclaimer:
          'This evidence package was generated by automated forensic tools and should be verified by qualified professionals.',
        chainOfCustody: 'All evidence items include detailed chain of custody documentation.',
        retention: 'Evidence should be retained according to applicable legal requirements.',
      },
    };

    const filename = path.join(
      this.outputDirectory,
      `enhanced_evidence_package_${this.investigationId}.json`
    );

    try {
      fs.writeFileSync(filename, JSON.stringify(packageData, null, 2));

      logger.info('Enhanced evidence package exported', {
        filename,
        evidenceCount: this.evidenceItems.size,
        packageSize: fs.statSync(filename).size,
      });

      return filename;
    } catch (error: any) {
      logger.error('Failed to export evidence package', {
        error: error.message,
        filename,
      });
      throw new Error(`Failed to export evidence package: ${error.message}`);
    }
  }

  /**
   * Generate audit report
   */
  generateAuditReport(): {
    summary: any;
    evidenceItems: any[];
    integrityStatus: any;
    chainOfCustodyReport: any[];
  } {
    const evidenceArray = Array.from(this.evidenceItems.values());

    const summary = {
      totalEvidence: evidenceArray.length,
      categoriesUsed: new Set(evidenceArray.map(e => e.category.id)).size,
      integrityChecks: evidenceArray.reduce((sum, e) => sum + e.integrityChecks.length, 0),
      chainOfCustodyEntries: evidenceArray.reduce((sum, e) => sum + e.chainOfCustody.length, 0),
      attachments: evidenceArray.reduce((sum, e) => sum + e.attachments.length, 0),
    };

    const integrityStatus = {
      totalChecks: summary.integrityChecks,
      passedChecks: evidenceArray.reduce(
        (sum, e) => sum + e.integrityChecks.filter(check => check.isValid).length,
        0
      ),
      failedChecks: evidenceArray.reduce(
        (sum, e) => sum + e.integrityChecks.filter(check => !check.isValid).length,
        0
      ),
    };

    const chainOfCustodyReport = evidenceArray.map(evidence => ({
      evidenceId: evidence.evidenceId,
      description: evidence.description,
      category: evidence.category.name,
      custodyEntries: evidence.chainOfCustody.length,
      lastAction: evidence.chainOfCustody[evidence.chainOfCustody.length - 1],
      integrityStatus: evidence.integrityChecks[evidence.integrityChecks.length - 1]?.isValid
        ? 'VALID'
        : 'INVALID',
    }));

    return {
      summary,
      evidenceItems: evidenceArray.map(e => ({
        id: e.evidenceId,
        type: e.evidenceType,
        description: e.description,
        category: e.category.name,
        timestamp: e.timestamp,
        integrityValid: e.integrityChecks[e.integrityChecks.length - 1]?.isValid,
      })),
      integrityStatus,
      chainOfCustodyReport,
    };
  }

  private createPackageHash(): string {
    const evidenceData = Array.from(this.evidenceItems.values())
      .map(e => ({ id: e.evidenceId, hash: e.hashValue }))
      .sort((a, b) => a.id.localeCompare(b.id));

    return crypto.createHash('sha256').update(JSON.stringify(evidenceData)).digest('hex');
  }

  /**
   * Get evidence statistics
   */
  getStatistics(): {
    totalEvidence: number;
    byCategory: Record<string, number>;
    byType: Record<string, number>;
    integrityStatus: { valid: number; invalid: number };
  } {
    const evidenceArray = Array.from(this.evidenceItems.values());

    const byCategory: Record<string, number> = {};
    const byType: Record<string, number> = {};
    let validIntegrity = 0;
    let invalidIntegrity = 0;

    evidenceArray.forEach(evidence => {
      // Count by category
      const categoryName = evidence.category.name;
      byCategory[categoryName] = (byCategory[categoryName] || 0) + 1;

      // Count by type
      byType[evidence.evidenceType] = (byType[evidence.evidenceType] || 0) + 1;

      // Count integrity status
      const lastIntegrityCheck = evidence.integrityChecks[evidence.integrityChecks.length - 1];
      if (lastIntegrityCheck?.isValid) {
        validIntegrity++;
      } else {
        invalidIntegrity++;
      }
    });

    return {
      totalEvidence: evidenceArray.length,
      byCategory,
      byType,
      integrityStatus: {
        valid: validIntegrity,
        invalid: invalidIntegrity,
      },
    };
  }

  /**
   * Get all evidence items
   */
  getAllEvidence(): EnhancedEvidenceItem[] {
    return Array.from(this.evidenceItems.values());
  }

  /**
   * Get evidence by ID
   */
  getEvidence(evidenceId: string): EnhancedEvidenceItem | undefined {
    return this.evidenceItems.get(evidenceId);
  }

  /**
   * Search evidence by criteria
   */
  searchEvidence(criteria: {
    category?: string;
    type?: string;
    description?: string;
    tags?: string[];
  }): EnhancedEvidenceItem[] {
    const evidenceArray = Array.from(this.evidenceItems.values());

    return evidenceArray.filter(evidence => {
      if (criteria.category && evidence.category.id !== criteria.category) {
        return false;
      }

      if (criteria.type && evidence.evidenceType !== criteria.type) {
        return false;
      }

      if (
        criteria.description &&
        !evidence.description.toLowerCase().includes(criteria.description.toLowerCase())
      ) {
        return false;
      }

      if (criteria.tags && criteria.tags.length > 0) {
        const evidenceTags = evidence.category.tags;
        if (!criteria.tags.some(tag => evidenceTags.includes(tag))) {
          return false;
        }
      }

      return true;
    });
  }
}
