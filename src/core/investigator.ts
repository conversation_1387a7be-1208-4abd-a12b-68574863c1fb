import { v4 as uuidv4 } from 'uuid';
import crypto from 'crypto';
import fs from 'fs';
import {
  InvestigationConfig,
  TransactionInfo,
  AddressInfo,
  InvestigationResults,
  UserInput,
  EvidenceItem,
  AuditLogEntry,
  ChainOfCustodyEntry,
} from '../types';
import { BitcoinAPIService } from '../services/api';
import { AnalysisService } from '../services/analysis';
import { EnhancedEvidenceService } from '../services/evidence';
import { AdvancedBitcoinTrackingService } from '../services/advanced-tracking';
import { EnhancedRiskAssessmentService } from '../services/enhanced-risk-assessment';
import { UnifiedReportingService } from '../services/unified-reporting';
import {
  validateBitcoinAddress,
  validateTransactionId,
  getBitcoinAddressType,
} from '../utils/validation';
import {
  logger,
  logInvestigationStart,
  logInvestigationEnd,
  logEvidenceCreated,
  logAuditEvent,
} from '../utils/logger';
import {
  <PERSON><PERSON>r<PERSON><PERSON><PERSON>,
  ForensicsError,
  ErrorCode,
  setupGlobalErrorHandling,
} from '../utils/error-handler';
import { InputValidator, SecurityMiddleware } from '../utils/security';
import { monitoring } from '../utils/monitoring';
import { DEFAULT_CONFIG } from '../config/default';

export class BitcoinForensicsInvestigator {
  private config: InvestigationConfig;
  private apiService: BitcoinAPIService;
  private analysisService: AnalysisService;
  private enhancedEvidenceService: EnhancedEvidenceService;
  private advancedTrackingService: AdvancedBitcoinTrackingService;
  private enhancedRiskService: EnhancedRiskAssessmentService;
  private unifiedReportingService: UnifiedReportingService;
  private investigationId: string;
  private evidenceItems: EvidenceItem[] = [];
  private auditLog: AuditLogEntry[] = [];
  private userInput?: UserInput;

  constructor(config: Partial<InvestigationConfig> = {}) {
    try {
      // Setup global error handling
      setupGlobalErrorHandling();

      this.config = { ...DEFAULT_CONFIG, ...config };
      this.apiService = new BitcoinAPIService(this.config);
      this.analysisService = new AnalysisService();
      this.investigationId = uuidv4();

      // Validate configuration
      this.validateConfiguration();

      // Create output directory
      this.ensureOutputDirectory();

      // Initialize enhanced evidence service
      this.enhancedEvidenceService = new EnhancedEvidenceService(
        this.investigationId,
        this.config.outputDirectory
      );

      // Initialize advanced tracking service
      this.advancedTrackingService = new AdvancedBitcoinTrackingService(this.apiService);

      // Initialize enhanced risk assessment service
      this.enhancedRiskService = new EnhancedRiskAssessmentService();

      // Initialize unified reporting service
      this.unifiedReportingService = new UnifiedReportingService(this.config.outputDirectory);

      // Log initialization
      this.logAuditEvent('investigation_initialized', {
        investigationId: this.investigationId,
        config: this.config,
      });

      logger.info(`Initialized Bitcoin Forensics Investigator with enhanced evidence tracking`, {
        investigationId: this.investigationId,
      });
    } catch (error) {
      const errorHandler = ErrorHandler.getInstance();
      const context = errorHandler.handleError(error as Error, { phase: 'initialization' });
      throw new ForensicsError(
        ErrorCode.CONFIGURATION_ERROR,
        'Failed to initialize Bitcoin Forensics Investigator',
        context.details
      );
    }
  }

  private ensureOutputDirectory(): void {
    if (!fs.existsSync(this.config.outputDirectory)) {
      fs.mkdirSync(this.config.outputDirectory, { recursive: true });
    }
  }

  private logAuditEvent(
    action: string,
    details: Record<string, any>,
    userId: string = 'system'
  ): void {
    const auditEntry: AuditLogEntry = {
      timestamp: new Date().toISOString(),
      investigationId: this.investigationId,
      action,
      details,
      userId,
      ipAddress: 'localhost', // In a real system, this would be the actual IP
    };

    this.auditLog.push(auditEntry);
    logAuditEvent(this.investigationId, action, details);
  }

  private createEvidenceItem(
    evidenceType: string,
    description: string,
    data: Record<string, any>
  ): EvidenceItem {
    const evidenceId = uuidv4();

    // Create hash of the evidence data for integrity
    const evidenceHash = crypto
      .createHash('sha256')
      .update(JSON.stringify(data, null, 0))
      .digest('hex');

    // Initialize chain of custody
    const chainOfCustody: ChainOfCustodyEntry[] = [
      {
        timestamp: new Date().toISOString(),
        action: 'evidence_created',
        userId: 'system',
        description: `Evidence item created: ${description}`,
      },
    ];

    const evidenceItem: EvidenceItem = {
      evidenceId,
      investigationId: this.investigationId,
      evidenceType: evidenceType as any,
      description,
      data,
      timestamp: new Date().toISOString(),
      hashValue: evidenceHash,
      chainOfCustody,
    };

    this.evidenceItems.push(evidenceItem);
    logEvidenceCreated(evidenceId, evidenceType, description);

    this.logAuditEvent('evidence_created', {
      evidenceId,
      evidenceType,
      description,
      hash: evidenceHash,
    });

    return evidenceItem;
  }

  /**
   * Validate configuration
   */
  private validateConfiguration(): void {
    InputValidator.validateApiConfig({
      apiBaseUrl: this.config.apiBaseUrl,
      requestTimeout: this.config.requestTimeout,
      rateLimitDelay: this.config.rateLimitDelay,
      maxRetries: this.config.maxRetries,
    });

    if (this.config.maxDepth < 1 || this.config.maxDepth > 20) {
      throw new ForensicsError(
        ErrorCode.CONFIGURATION_ERROR,
        'Maximum depth must be between 1 and 20'
      );
    }
  }

  async startInvestigation(userInput: UserInput): Promise<InvestigationResults> {
    const startTime = performance.now();
    let success = false;

    try {
      // Validate user input with security checks
      InputValidator.validateInvestigationParams({
        initialTxid: userInput.initialTxid,
        targetAddress: userInput.targetAddress,
        maxDepth: userInput.maxDepth,
        victimName: userInput.victimName,
        caseDescription: userInput.caseDescription,
      });

      this.userInput = userInput;

      logInvestigationStart(this.investigationId, userInput);

      this.logAuditEvent('investigation_started', {
        initialTxid: userInput.initialTxid,
        targetAddress: userInput.targetAddress,
        maxDepth: userInput.maxDepth,
        victimName: userInput.victimName,
      });

      logger.info('🕵️  Starting Bitcoin forensic investigation', {
        investigationId: this.investigationId,
        txid: userInput.initialTxid,
        address: userInput.targetAddress,
        victimName: userInput.victimName,
      });

      // Additional validation using existing validators
      if (!validateTransactionId(userInput.initialTxid)) {
        throw new ForensicsError(
          ErrorCode.INVALID_TRANSACTION_ID,
          `Invalid transaction ID: ${userInput.initialTxid}`
        );
      }

      if (!validateBitcoinAddress(userInput.targetAddress)) {
        throw new ForensicsError(
          ErrorCode.INVALID_BITCOIN_ADDRESS,
          `Invalid Bitcoin address: ${userInput.targetAddress}`
        );
      }

      // Perform transaction tracing with monitoring
      monitoring.recordPerformance('transaction_tracing_start', 0, true, {
        investigationId: this.investigationId,
        maxDepth: userInput.maxDepth,
      });

      const { transactions, addresses } = await SecurityMiddleware.secureApiRequest(
        () =>
          this.traceTransactions(
            userInput.initialTxid,
            userInput.targetAddress,
            userInput.maxDepth
          ),
        `investigation_${this.investigationId}`,
        { phase: 'transaction_tracing' }
      );

      // Create basic results
      const basicResults = {
        transactionCount: transactions.length,
        addressCount: addresses.length,
        totalAmount: transactions.reduce((sum, tx) => sum + tx.amountBtc, 0),
      };

      // Create enhanced evidence for transactions and addresses
      transactions.forEach(tx => {
        const evidence = this.enhancedEvidenceService.createTransactionEvidence(tx);
        this.enhancedEvidenceService.updateChainOfCustody(
          evidence.evidenceId,
          'evidence_collected',
          `Transaction evidence collected during investigation`,
          'investigator'
        );
      });

      addresses.forEach(addr => {
        const evidence = this.enhancedEvidenceService.createAddressEvidence(addr);
        this.enhancedEvidenceService.updateChainOfCustody(
          evidence.evidenceId,
          'evidence_collected',
          `Address evidence collected during investigation`,
          'investigator'
        );
      });

      // Perform pattern analysis
      const patterns = this.analysisService.analyzeTransactionPatterns(transactions);
      const suspiciousActivity = this.analysisService.detectSuspiciousActivity(transactions);

      // Perform advanced tracking analysis
      const advancedTracking = await this.advancedTrackingService.performAdvancedTracking(
        transactions,
        addresses
      );

      // Perform enhanced risk assessment
      const enhancedRisk = await this.enhancedRiskService.performEnhancedRiskAssessment(
        transactions,
        addresses,
        patterns,
        suspiciousActivity
      );

      // Generate enhanced evidence package
      const evidencePackagePath = await this.enhancedEvidenceService.exportEvidencePackage();
      const evidenceStats = this.enhancedEvidenceService.getStatistics();
      const auditReport = this.enhancedEvidenceService.generateAuditReport();

      // Create investigation results structure
      const results: InvestigationResults = {
        investigationId: this.investigationId,
        timestamp: new Date().toISOString(),
        inputParameters: {
          initialTxid: userInput.initialTxid,
          targetAddress: userInput.targetAddress,
          maxDepth: userInput.maxDepth,
          victimName: userInput.victimName,
          caseDescription: userInput.caseDescription,
        },
        basicResults,
        detailedTransactions: transactions,
        addressAnalysis: addresses,
        advancedAnalysis: {
          transactionPatterns: patterns,
          suspiciousActivity: suspiciousActivity,
          riskAssessment: {
            compositeRiskScore: enhancedRisk.compositeRiskScore,
            finalRiskLevel: enhancedRisk.finalRiskLevel,
            baseRiskScore: enhancedRisk.baseRiskScore,
            suspicionScore: enhancedRisk.suspicionScore,
            riskFactors: enhancedRisk.riskFactorNames,
            suspiciousActivities: enhancedRisk.suspiciousActivities,
            recommendations: enhancedRisk.recommendations,
          },
        },
        investigationSummary: {
          status: 'completed',
          keyMetrics: {
            totalTransactions: transactions.length,
            totalAmountBtc: basicResults.totalAmount,
            uniqueAddresses: addresses.length,
            maximumDepth: Math.max(...transactions.map(tx => tx.depth), 0),
            riskLevel: 'UNKNOWN', // Will be determined by analysis
          },
          keyConcerns: [],
          investigationQuality: {
            qualityScore: 0,
            qualityLevel: 'MEDIUM',
            qualityFactors: [],
            completenessPercentage: 0,
          },
          nextSteps: [],
          timeline: [],
        },
        evidencePackage: this.evidenceItems,
        auditTrail: this.auditLog,
        enhancedEvidence: {
          totalItems: evidenceStats.totalEvidence,
          categories: Object.keys(evidenceStats.byCategory),
          integrityStatus: evidenceStats.integrityStatus,
          packagePath: evidencePackagePath,
          auditReport,
        },
        advancedTracking,
        enhancedRiskAssessment: enhancedRisk,
      };

      // Generate comprehensive reports
      const reportResults = await this.unifiedReportingService.generateReports(
        results,
        enhancedRisk,
        advancedTracking,
        {
          includeVictimFriendly: true,
          includeTechnical: true,
          outputFormats: ['html', 'text', 'json'],
        }
      );

      // Add report paths to results
      results.enhancedEvidence!.victimReportPath = reportResults.victimReport;
      results.enhancedEvidence!.technicalReports = reportResults.technicalReports;

      logInvestigationEnd(this.investigationId, basicResults);

      this.logAuditEvent('investigation_completed', {
        transactionCount: transactions.length,
        addressCount: addresses.length,
        totalAmount: basicResults.totalAmount,
      });

      // Record successful investigation
      success = true;
      const duration = performance.now() - startTime;
      monitoring.recordInvestigation(true, duration);
      monitoring.recordPerformance('investigation_complete', duration, true, {
        investigationId: this.investigationId,
        transactionCount: transactions.length,
        addressCount: addresses.length,
      });

      return results;
    } catch (error: any) {
      const duration = performance.now() - startTime;
      const errorHandler = ErrorHandler.getInstance();

      // Handle the error with proper context
      const errorContext = errorHandler.handleError(error, {
        investigationId: this.investigationId,
        phase: 'investigation',
        userInput: this.userInput,
        duration,
      });

      // Record failed investigation
      monitoring.recordInvestigation(false, duration);
      monitoring.recordPerformance('investigation_failed', duration, false, {
        investigationId: this.investigationId,
        errorCode: errorContext.code,
      });

      logger.error('Investigation failed', {
        investigationId: this.investigationId,
        error: error.message,
        errorCode: errorContext.code,
        userFriendlyMessage: errorContext.userFriendlyMessage,
      });

      this.logAuditEvent('investigation_failed', {
        error: error.message,
        errorCode: errorContext.code,
        stack: error.stack,
        duration,
      });

      // Re-throw as ForensicsError if not already
      if (error instanceof ForensicsError) {
        throw error;
      } else {
        throw new ForensicsError(
          ErrorCode.INVESTIGATION_FAILED,
          'Investigation failed due to an unexpected error',
          { originalError: error.message, investigationId: this.investigationId },
          errorContext.userFriendlyMessage
        );
      }
    }
  }

  private async traceTransactions(
    initialTxid: string,
    targetAddress: string,
    maxDepth: number
  ): Promise<{ transactions: TransactionInfo[]; addresses: AddressInfo[] }> {
    const transactions: TransactionInfo[] = [];
    const addressMap = new Map<string, AddressInfo>();
    const visitedTxs = new Set<string>([initialTxid]);
    const queue: Array<{ txid: string; address: string; depth: number }> = [
      { txid: initialTxid, address: targetAddress, depth: 0 },
    ];

    // Add initial address to the map
    addressMap.set(targetAddress, {
      address: targetAddress,
      addressType: getBitcoinAddressType(targetAddress),
      totalReceived: 0,
      totalSent: 0,
      balance: 0,
      transactionCount: 0,
      firstSeen: new Date().toISOString(),
    });

    while (queue.length > 0) {
      const { txid: currentTxid, address: sourceAddress, depth } = queue.shift()!;

      if (depth >= maxDepth) {
        logger.info(
          `Reached maximum depth ${maxDepth} for address ${sourceAddress.substring(0, 8)}...`
        );
        continue;
      }

      logger.info(`Processing transaction ${currentTxid} at depth ${depth}`);

      // Get transaction data
      const txResponse = await this.apiService.getTransaction(currentTxid);
      if (!txResponse.success || !txResponse.data) {
        logger.warn(`Failed to fetch transaction ${currentTxid}: ${txResponse.error}`);
        continue;
      }

      const txData = txResponse.data;

      // Find outputs that go TO the source address
      const relevantOutputs = txData.vout
        .map((vout, index) => ({ vout, index }))
        .filter(({ vout }) => vout.scriptpubkey_address === sourceAddress);

      // For each relevant output, check if it gets spent
      for (const { vout, index } of relevantOutputs) {
        const outspendResponse = await this.apiService.getOutspend(currentTxid, index);
        if (!outspendResponse.success || !outspendResponse.data?.spent) {
          continue;
        }

        const outspendData = outspendResponse.data;
        const spendingTxid = outspendData.txid!;

        if (visitedTxs.has(spendingTxid)) {
          continue;
        }

        visitedTxs.add(spendingTxid);

        // Get the spending transaction
        const spendingTxResponse = await this.apiService.getTransaction(spendingTxid);
        if (!spendingTxResponse.success || !spendingTxResponse.data) {
          continue;
        }

        const spendingTxData = spendingTxResponse.data;

        // Analyze where the funds went
        for (const nextVout of spendingTxData.vout) {
          const newAddress = nextVout.scriptpubkey_address;
          if (!newAddress || newAddress === sourceAddress) {
            continue;
          }

          const amountBtc = nextVout.value / 100_000_000;

          // Create transaction info
          const transactionInfo: TransactionInfo = {
            txid: spendingTxid,
            fromAddress: sourceAddress,
            toAddress: newAddress,
            amountBtc,
            depth: depth + 1,
            timestamp: new Date().toISOString(),
            blockHeight: spendingTxData.status.block_height,
            confirmations: spendingTxData.status.confirmed,
            investigationId: this.investigationId,
            fees: spendingTxData.fee / 100_000_000,
            inputCount: spendingTxData.vin.length,
            outputCount: spendingTxData.vout.length,
          };

          transactions.push(transactionInfo);

          // Update address info
          if (!addressMap.has(newAddress)) {
            addressMap.set(newAddress, {
              address: newAddress,
              addressType: getBitcoinAddressType(newAddress),
              totalReceived: 0,
              totalSent: 0,
              balance: 0,
              transactionCount: 0,
              firstSeen: new Date().toISOString(),
            });
          }

          const addressInfo = addressMap.get(newAddress)!;
          addressInfo.totalReceived += amountBtc;
          addressInfo.transactionCount++;
          addressInfo.lastSeen = new Date().toISOString();

          // Create evidence item
          this.createEvidenceItem(
            'transaction',
            `Transaction ${spendingTxid}: ${amountBtc.toFixed(8)} BTC from ${sourceAddress.substring(0, 8)}... to ${newAddress.substring(0, 8)}...`,
            {
              transactionInfo,
              rawTransactionData: spendingTxData,
              discoveryContext: {
                parentTxid: currentTxid,
                outputIndex: index,
                discoveryDepth: depth,
              },
            }
          );

          // Continue tracing
          queue.push({ txid: spendingTxid, address: newAddress, depth: depth + 1 });
        }
      }
    }

    return {
      transactions,
      addresses: Array.from(addressMap.values()),
    };
  }

  getInvestigationId(): string {
    return this.investigationId;
  }

  getEvidenceItems(): EvidenceItem[] {
    return [...this.evidenceItems];
  }

  getAuditLog(): AuditLogEntry[] {
    return [...this.auditLog];
  }
}
