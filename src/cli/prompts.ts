import inquirer from 'inquirer';
import chalk from 'chalk';
import { UserInput } from '../types';
import {
  validateBitcoinAddress,
  validateTransactionId,
  validateBtcAmount,
  validateEmail,
  validateDate,
} from '../utils/validation';

export class CLIPrompts {
  async getVictimInformation(): Promise<Partial<UserInput>> {
    console.log(chalk.blue('\n📋 VICTIM INFORMATION'));
    console.log(chalk.gray('Please provide the following information about the scam incident:\n'));

    const answers = await inquirer.prompt([
      {
        type: 'input',
        name: 'victimName',
        message: 'Your full name:',
        validate: (input: string) => {
          if (!input || input.trim().length === 0) {
            return 'Name is required';
          }
          if (input.trim().length < 2) {
            return 'Name must be at least 2 characters';
          }
          return true;
        },
        filter: (input: string) => input.trim(),
      },
      {
        type: 'input',
        name: 'contactInfo',
        message: 'Your email address (optional):',
        validate: (input: string) => {
          if (!input || input.trim().length === 0) {
            return true; // Optional field
          }
          return validateEmail(input) || 'Please enter a valid email address';
        },
        filter: (input: string) => input.trim(),
      },
      {
        type: 'input',
        name: 'incidentDate',
        message: 'Date of the scam incident (YYYY-MM-DD, optional):',
        validate: (input: string) => {
          if (!input || input.trim().length === 0) {
            return true; // Optional field
          }
          return validateDate(input) || 'Please enter a valid date in YYYY-MM-DD format';
        },
        filter: (input: string) => input.trim(),
      },
      {
        type: 'editor',
        name: 'caseDescription',
        message: 'Describe what happened (this will help with the investigation):',
        validate: (input: string) => {
          if (!input || input.trim().length === 0) {
            return 'Case description is required';
          }
          if (input.trim().length < 10) {
            return 'Please provide a more detailed description (at least 10 characters)';
          }
          return true;
        },
        filter: (input: string) => input.trim(),
      },
    ]);

    return answers;
  }

  async getTransactionDetails(): Promise<Partial<UserInput>> {
    console.log(chalk.blue('\n🔍 TRANSACTION DETAILS'));
    console.log(chalk.gray('Please provide the Bitcoin transaction information:\n'));

    const answers = await inquirer.prompt([
      {
        type: 'input',
        name: 'initialTxid',
        message: 'Transaction ID (TXID) where you sent Bitcoin to the scammer:',
        validate: (input: string) => {
          if (!input || input.trim().length === 0) {
            return 'Transaction ID is required';
          }
          return (
            validateTransactionId(input.trim()) ||
            'Please enter a valid 64-character transaction ID'
          );
        },
        filter: (input: string) => input.trim(),
      },
      {
        type: 'input',
        name: 'targetAddress',
        message: "Bitcoin address where you sent the funds (scammer's address):",
        validate: (input: string) => {
          if (!input || input.trim().length === 0) {
            return 'Bitcoin address is required';
          }
          return validateBitcoinAddress(input.trim()) || 'Please enter a valid Bitcoin address';
        },
        filter: (input: string) => input.trim(),
      },
      {
        type: 'number',
        name: 'scamAmount',
        message: 'Amount of Bitcoin you lost (in BTC):',
        validate: (input: number) => {
          if (!input || isNaN(input)) {
            return 'Please enter a valid amount';
          }
          return (
            validateBtcAmount(input) ||
            'Please enter a valid Bitcoin amount (0 < amount <= 21,000,000)'
          );
        },
      },
    ]);

    return answers;
  }

  async getInvestigationSettings(): Promise<Partial<UserInput>> {
    console.log(chalk.blue('\n⚙️  INVESTIGATION SETTINGS'));
    console.log(chalk.gray('Configure how deep you want to trace the transactions:\n'));

    const answers = await inquirer.prompt([
      {
        type: 'list',
        name: 'maxDepth',
        message: 'Investigation depth (how many transaction hops to follow):',
        choices: [
          {
            name: '🚀 Quick (3 hops) - Fast results, basic tracing',
            value: 3,
          },
          {
            name: '🔍 Standard (5 hops) - Balanced speed and thoroughness',
            value: 5,
          },
          {
            name: '🕵️  Deep (7 hops) - Comprehensive investigation',
            value: 7,
          },
          {
            name: '🔬 Maximum (10 hops) - Most thorough, may take longer',
            value: 10,
          },
        ],
        default: 5,
      },
      {
        type: 'confirm',
        name: 'enableAdvancedAnalysis',
        message: 'Enable advanced pattern analysis and AI insights?',
        default: true,
      },
      {
        type: 'confirm',
        name: 'generateReports',
        message: 'Generate comprehensive reports and evidence package?',
        default: true,
      },
    ]);

    return answers;
  }

  async confirmInvestigation(userInput: UserInput): Promise<boolean> {
    console.log(chalk.yellow('\n📋 INVESTIGATION SUMMARY'));
    console.log(chalk.gray('Please review the information before starting:\n'));

    console.log(chalk.white('Victim Information:'));
    console.log(`  Name: ${chalk.cyan(userInput.victimName)}`);
    if (userInput.contactInfo) {
      console.log(`  Email: ${chalk.cyan(userInput.contactInfo)}`);
    }
    if (userInput.incidentDate) {
      console.log(`  Incident Date: ${chalk.cyan(userInput.incidentDate)}`);
    }

    console.log(chalk.white('\nTransaction Details:'));
    console.log(`  Transaction ID: ${chalk.cyan(userInput.initialTxid)}`);
    console.log(`  Scammer Address: ${chalk.cyan(userInput.targetAddress)}`);
    console.log(`  Amount Lost: ${chalk.red(userInput.scamAmount.toFixed(8))} BTC`);

    console.log(chalk.white('\nInvestigation Settings:'));
    console.log(`  Max Depth: ${chalk.cyan(userInput.maxDepth)} hops`);

    console.log(chalk.white('\nCase Description:'));
    console.log(
      chalk.gray(
        `  ${userInput.caseDescription.substring(0, 200)}${userInput.caseDescription.length > 200 ? '...' : ''}`
      )
    );

    const { confirmed } = await inquirer.prompt([
      {
        type: 'confirm',
        name: 'confirmed',
        message: '\nStart the investigation with this information?',
        default: true,
      },
    ]);

    return confirmed;
  }

  async askForHelp(): Promise<string> {
    const { helpTopic } = await inquirer.prompt([
      {
        type: 'list',
        name: 'helpTopic',
        message: 'What do you need help with?',
        choices: [
          {
            name: '🔍 How to find my transaction ID (TXID)',
            value: 'txid',
          },
          {
            name: '📍 How to find the Bitcoin address I sent to',
            value: 'address',
          },
          {
            name: '💰 How to determine the amount I lost',
            value: 'amount',
          },
          {
            name: '⚖️  Legal advice and next steps',
            value: 'legal',
          },
          {
            name: '🛡️  How to protect myself from future scams',
            value: 'protection',
          },
          {
            name: '📞 How to report the scam to authorities',
            value: 'reporting',
          },
          {
            name: '🔙 Go back to main menu',
            value: 'back',
          },
        ],
      },
    ]);

    return helpTopic;
  }

  async askToContinue(message: string = 'Continue?'): Promise<boolean> {
    const { shouldContinue } = await inquirer.prompt([
      {
        type: 'confirm',
        name: 'shouldContinue',
        message,
        default: true,
      },
    ]);

    return shouldContinue;
  }

  async selectOutputFormat(): Promise<string[]> {
    const { formats } = await inquirer.prompt([
      {
        type: 'checkbox',
        name: 'formats',
        message: 'Select output formats for your investigation report:',
        choices: [
          {
            name: '📄 PDF Report (recommended for legal proceedings)',
            value: 'pdf',
            checked: true,
          },
          {
            name: '📊 HTML Report with interactive charts',
            value: 'html',
            checked: true,
          },
          {
            name: '📋 Text Report (simple format)',
            value: 'txt',
            checked: false,
          },
          {
            name: '💾 JSON Data (for technical analysis)',
            value: 'json',
            checked: false,
          },
          {
            name: '📈 CSV Data (for spreadsheet analysis)',
            value: 'csv',
            checked: false,
          },
        ],
        validate: (choices: string[]) => {
          if (choices.length === 0) {
            return 'Please select at least one output format';
          }
          return true;
        },
      },
    ]);

    return formats;
  }

  displayWelcome(): void {
    console.clear();
    console.log(
      chalk.blue.bold(`
╔══════════════════════════════════════════════════════════════════════════════╗
║                    🔍 Bitcoin Forensic Investigation Tool v3.0                ║
║                          For Bitcoin Scam Victims                            ║
╚══════════════════════════════════════════════════════════════════════════════╝
`)
    );

    console.log(chalk.white('🎯 PURPOSE:'));
    console.log(chalk.gray('   This tool helps Bitcoin scam victims trace their stolen funds and'));
    console.log(chalk.gray('   generate professional evidence packages for law enforcement.\n'));

    console.log(chalk.white('✨ FEATURES:'));
    console.log(chalk.gray('   • Automated Bitcoin transaction tracing'));
    console.log(chalk.gray('   • AI-powered suspicious activity detection'));
    console.log(chalk.gray('   • Professional evidence collection'));
    console.log(chalk.gray('   • Comprehensive reports for legal proceedings'));
    console.log(chalk.gray('   • User-friendly interface for non-technical users\n'));

    console.log(chalk.yellow('⚠️  IMPORTANT LEGAL NOTICE:'));
    console.log(chalk.gray('   This tool is for legitimate investigation purposes only.'));
    console.log(chalk.gray('   Always consult with law enforcement and legal professionals.'));
    console.log(chalk.gray('   Keep all generated reports as evidence for your case.\n'));

    console.log(chalk.green('🚀 Ready to start your investigation!\n'));
  }

  displayHelp(topic: string): void {
    console.log(chalk.blue(`\n📚 HELP: ${topic.toUpperCase()}\n`));

    switch (topic) {
      case 'txid':
        console.log(chalk.white('How to find your Transaction ID (TXID):'));
        console.log(chalk.gray("1. Check your Bitcoin wallet's transaction history"));
        console.log(
          chalk.gray('2. Look for the transaction where you sent Bitcoin to the scammer')
        );
        console.log(chalk.gray('3. The TXID is a 64-character string of letters and numbers'));
        console.log(chalk.gray('4. You can also search on blockchain explorers like:'));
        console.log(chalk.gray('   • https://mempool.space'));
        console.log(chalk.gray('   • https://blockstream.info'));
        console.log(chalk.gray('   • https://blockchain.info'));
        break;

      case 'address':
        console.log(chalk.white('How to find the Bitcoin address you sent to:'));
        console.log(chalk.gray("1. Check your wallet's transaction details"));
        console.log(chalk.gray('2. Look for the "To" or "Recipient" address'));
        console.log(chalk.gray('3. Bitcoin addresses start with:'));
        console.log(chalk.gray('   • 1 or 3 (Legacy addresses)'));
        console.log(chalk.gray('   • bc1 (SegWit addresses)'));
        console.log(chalk.gray('4. Copy the full address exactly as shown'));
        break;

      case 'amount':
        console.log(chalk.white('How to determine the amount you lost:'));
        console.log(chalk.gray("1. Check your wallet's transaction history"));
        console.log(chalk.gray('2. Look at the amount sent in the transaction'));
        console.log(chalk.gray('3. Enter the amount in BTC (not USD)'));
        console.log(chalk.gray('4. Example: 0.05 BTC, not $2,000'));
        break;

      case 'legal':
        console.log(chalk.white('Legal advice and next steps:'));
        console.log(chalk.gray('1. File a report with your local police'));
        console.log(chalk.gray("2. Report to the FBI's IC3 (if in the US)"));
        console.log(chalk.gray("3. Contact your country's cybercrime unit"));
        console.log(chalk.gray('4. Keep all evidence and documentation'));
        console.log(chalk.gray('5. Consider consulting with a lawyer'));
        console.log(chalk.yellow('⚠️  This tool does not provide legal advice'));
        break;

      case 'protection':
        console.log(chalk.white('How to protect yourself from future scams:'));
        console.log(chalk.gray('1. Never send Bitcoin to unknown parties'));
        console.log(chalk.gray('2. Verify all investment opportunities independently'));
        console.log(chalk.gray('3. Be suspicious of guaranteed returns'));
        console.log(chalk.gray('4. Use reputable exchanges and wallets'));
        console.log(chalk.gray('5. Enable two-factor authentication'));
        console.log(chalk.gray('6. Keep your private keys secure'));
        break;

      case 'reporting':
        console.log(chalk.white('How to report the scam to authorities:'));
        console.log(chalk.gray('🇺🇸 United States:'));
        console.log(chalk.gray('   • FBI IC3: https://ic3.gov'));
        console.log(chalk.gray('   • FTC: https://reportfraud.ftc.gov'));
        console.log(chalk.gray('🇬🇧 United Kingdom:'));
        console.log(chalk.gray('   • Action Fraud: https://actionfraud.police.uk'));
        console.log(chalk.gray('🇨🇦 Canada:'));
        console.log(chalk.gray('   • CAFC: https://antifraudcentre-centreantifraude.ca'));
        console.log(chalk.gray('🌍 Other countries:'));
        console.log(chalk.gray('   • Contact your local police cybercrime unit'));
        break;
    }

    console.log(chalk.gray('\nPress Enter to continue...'));
  }

  displayError(error: string): void {
    console.log(chalk.red(`\n❌ Error: ${error}\n`));
  }

  displaySuccess(message: string): void {
    console.log(chalk.green(`\n✅ ${message}\n`));
  }

  displayWarning(message: string): void {
    console.log(chalk.yellow(`\n⚠️  ${message}\n`));
  }

  displayInfo(message: string): void {
    console.log(chalk.blue(`\nℹ️  ${message}\n`));
  }
}
