import ora, { Ora } from 'ora';
import cliProgress from 'cli-progress';
import chalk from 'chalk';

export class ProgressIndicator {
  private spinner?: Ora;
  private progressBar?: cliProgress.SingleBar;

  /**
   * Start a spinner with a message
   */
  startSpinner(message: string): void {
    this.stopSpinner();
    this.spinner = ora({
      text: message,
      color: 'blue',
      spinner: 'dots',
    }).start();
  }

  /**
   * Update spinner message
   */
  updateSpinner(message: string): void {
    if (this.spinner) {
      this.spinner.text = message;
    }
  }

  /**
   * Stop spinner with success message
   */
  succeedSpinner(message?: string): void {
    if (this.spinner) {
      this.spinner.succeed(message);
      this.spinner = undefined;
    }
  }

  /**
   * Stop spinner with failure message
   */
  failSpinner(message?: string): void {
    if (this.spinner) {
      this.spinner.fail(message);
      this.spinner = undefined;
    }
  }

  /**
   * Stop spinner with warning message
   */
  warnSpinner(message?: string): void {
    if (this.spinner) {
      this.spinner.warn(message);
      this.spinner = undefined;
    }
  }

  /**
   * Stop spinner without message
   */
  stopSpinner(): void {
    if (this.spinner) {
      this.spinner.stop();
      this.spinner = undefined;
    }
  }

  /**
   * Start a progress bar
   */
  startProgressBar(total: number, message: string = 'Progress'): void {
    this.stopProgressBar();
    this.progressBar = new cliProgress.SingleBar({
      format: `${chalk.blue(message)} |${chalk.cyan('{bar}')}| {percentage}% | {value}/{total} | ETA: {eta}s`,
      barCompleteChar: '█',
      barIncompleteChar: '░',
      hideCursor: true,
    });
    this.progressBar.start(total, 0);
  }

  /**
   * Update progress bar
   */
  updateProgressBar(current: number, payload?: any): void {
    if (this.progressBar) {
      this.progressBar.update(current, payload);
    }
  }

  /**
   * Stop progress bar
   */
  stopProgressBar(): void {
    if (this.progressBar) {
      this.progressBar.stop();
      this.progressBar = undefined;
    }
  }

  /**
   * Display investigation phases
   */
  displayPhase(phase: string, description: string): void {
    console.log(chalk.blue.bold(`\n🔍 ${phase.toUpperCase()}`));
    console.log(chalk.gray(`${description}\n`));
  }

  /**
   * Display step completion
   */
  displayStepComplete(step: string, details?: string): void {
    console.log(chalk.green(`✅ ${step}`));
    if (details) {
      console.log(chalk.gray(`   ${details}`));
    }
  }

  /**
   * Display step warning
   */
  displayStepWarning(step: string, details?: string): void {
    console.log(chalk.yellow(`⚠️  ${step}`));
    if (details) {
      console.log(chalk.gray(`   ${details}`));
    }
  }

  /**
   * Display step error
   */
  displayStepError(step: string, details?: string): void {
    console.log(chalk.red(`❌ ${step}`));
    if (details) {
      console.log(chalk.gray(`   ${details}`));
    }
  }

  /**
   * Display investigation statistics
   */
  displayStats(stats: {
    transactionsFound: number;
    addressesDiscovered: number;
    totalAmount: number;
    depth: number;
    duration: number;
  }): void {
    console.log(chalk.blue('\n📊 INVESTIGATION STATISTICS'));
    console.log(chalk.gray('─'.repeat(50)));
    console.log(chalk.white(`Transactions Found: ${chalk.cyan(stats.transactionsFound)}`));
    console.log(chalk.white(`Addresses Discovered: ${chalk.cyan(stats.addressesDiscovered)}`));
    console.log(
      chalk.white(`Total Amount Traced: ${chalk.cyan(stats.totalAmount.toFixed(8))} BTC`)
    );
    console.log(chalk.white(`Maximum Depth: ${chalk.cyan(stats.depth)} hops`));
    console.log(
      chalk.white(`Investigation Time: ${chalk.cyan(Math.round(stats.duration))} seconds`)
    );
    console.log(chalk.gray('─'.repeat(50)));
  }

  /**
   * Display risk assessment
   */
  displayRiskAssessment(riskLevel: string, riskScore: number, concerns: string[]): void {
    console.log(chalk.blue('\n🚨 RISK ASSESSMENT'));
    console.log(chalk.gray('─'.repeat(50)));

    const riskColor = this.getRiskColor(riskLevel);
    console.log(chalk.white(`Risk Level: ${riskColor(riskLevel)}`));
    console.log(chalk.white(`Risk Score: ${chalk.cyan(riskScore)}/20`));

    if (concerns.length > 0) {
      console.log(chalk.white('\nKey Concerns:'));
      concerns.forEach(concern => {
        console.log(
          chalk.yellow(`  • ${concern.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}`)
        );
      });
    }

    console.log(chalk.gray('─'.repeat(50)));
  }

  /**
   * Display next steps
   */
  displayNextSteps(steps: string[]): void {
    if (steps.length === 0) return;

    console.log(chalk.blue('\n🎯 RECOMMENDED NEXT STEPS'));
    console.log(chalk.gray('─'.repeat(50)));

    steps.forEach((step, index) => {
      console.log(chalk.white(`${index + 1}. ${step}`));
    });

    console.log(chalk.gray('─'.repeat(50)));
  }

  /**
   * Display file generation progress
   */
  displayFileGeneration(files: string[]): void {
    console.log(chalk.blue('\n📁 GENERATING REPORTS'));
    console.log(chalk.gray('─'.repeat(50)));

    files.forEach(file => {
      console.log(chalk.green(`✅ Generated: ${file}`));
    });

    console.log(chalk.gray('─'.repeat(50)));
  }

  /**
   * Display investigation summary
   */
  displayInvestigationSummary(summary: {
    investigationId: string;
    victimName: string;
    totalAmount: number;
    riskLevel: string;
    filesGenerated: string[];
    outputDirectory: string;
  }): void {
    console.log(chalk.blue.bold('\n🎉 INVESTIGATION COMPLETE!'));
    console.log(chalk.gray('═'.repeat(80)));

    console.log(chalk.white(`Investigation ID: ${chalk.cyan(summary.investigationId)}`));
    console.log(chalk.white(`Victim: ${chalk.cyan(summary.victimName)}`));
    console.log(chalk.white(`Amount Traced: ${chalk.cyan(summary.totalAmount.toFixed(8))} BTC`));
    console.log(
      chalk.white(`Risk Level: ${this.getRiskColor(summary.riskLevel)(summary.riskLevel)}`)
    );

    console.log(chalk.white('\nGenerated Files:'));
    summary.filesGenerated.forEach(file => {
      console.log(chalk.gray(`  📄 ${file}`));
    });

    console.log(chalk.white(`\nAll files saved to: ${chalk.cyan(summary.outputDirectory)}`));

    console.log(chalk.gray('═'.repeat(80)));
    console.log(
      chalk.green.bold('Your investigation is complete! Please keep all files as evidence.')
    );
    console.log(chalk.yellow('Remember to report this scam to law enforcement authorities.'));
  }

  /**
   * Display API rate limiting message
   */
  displayRateLimit(waitTime: number): void {
    console.log(chalk.yellow(`\n⏳ API rate limit reached. Waiting ${waitTime} seconds...`));
  }

  /**
   * Display error with recovery suggestions
   */
  displayErrorWithSuggestions(error: string, suggestions: string[]): void {
    console.log(chalk.red(`\n❌ Error: ${error}`));

    if (suggestions.length > 0) {
      console.log(chalk.yellow('\n💡 Suggestions:'));
      suggestions.forEach(suggestion => {
        console.log(chalk.gray(`  • ${suggestion}`));
      });
    }
  }

  /**
   * Get color for risk level
   */
  private getRiskColor(riskLevel: string): (text: string) => string {
    switch (riskLevel.toUpperCase()) {
      case 'CRITICAL':
        return chalk.red.bold;
      case 'HIGH':
        return chalk.red;
      case 'MEDIUM':
        return chalk.yellow;
      case 'LOW':
        return chalk.blue;
      case 'MINIMAL':
        return chalk.green;
      default:
        return chalk.gray;
    }
  }

  /**
   * Clear console
   */
  clear(): void {
    console.clear();
  }

  /**
   * Add spacing
   */
  addSpacing(lines: number = 1): void {
    console.log('\n'.repeat(lines - 1));
  }

  /**
   * Display separator
   */
  displaySeparator(char: string = '─', length: number = 50): void {
    console.log(chalk.gray(char.repeat(length)));
  }
}
