import { logger } from './logger';

export enum ErrorCode {
  // Validation errors
  INVALID_BITCOIN_ADDRESS = 'INVALID_BITCOIN_ADDRESS',
  INVALID_TRANSACTION_ID = 'INVALID_TRANSACTION_ID',
  INVALID_INPUT_PARAMETERS = 'INVALID_INPUT_PARAMETERS',

  // API errors
  API_REQUEST_FAILED = 'API_REQUEST_FAILED',
  API_RATE_LIMITED = 'API_RATE_LIMITED',
  API_TIMEOUT = 'API_TIMEOUT',
  API_UNAUTHORIZED = 'API_UNAUTHORIZED',

  // Investigation errors
  INVESTIGATION_FAILED = 'INVESTIGATION_FAILED',
  TRANSACTION_NOT_FOUND = 'TRANSACTION_NOT_FOUND',
  ADDRESS_NOT_FOUND = 'ADDRESS_NOT_FOUND',
  INSUFFICIENT_DATA = 'INSUFFICIENT_DATA',

  // File system errors
  FILE_NOT_FOUND = 'FILE_NOT_FOUND',
  FILE_WRITE_ERROR = 'FILE_WRITE_ERROR',
  FILE_READ_ERROR = 'FILE_READ_ERROR',
  DIRECTORY_CREATE_ERROR = 'DIRECTORY_CREATE_ERROR',

  // Security errors
  SECURITY_VIOLATION = 'SECURITY_VIOLATION',
  UNAUTHORIZED_ACCESS = 'UNAUTHORIZED_ACCESS',
  DATA_INTEGRITY_ERROR = 'DATA_INTEGRITY_ERROR',

  // System errors
  MEMORY_LIMIT_EXCEEDED = 'MEMORY_LIMIT_EXCEEDED',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
  CONFIGURATION_ERROR = 'CONFIGURATION_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
}

export interface ErrorContext {
  code: ErrorCode;
  message: string;
  details?: Record<string, any>;
  timestamp: string;
  stack?: string;
  userFriendlyMessage?: string;
  recoverable: boolean;
  retryable: boolean;
}

export class ForensicsError extends Error {
  public readonly code: ErrorCode;
  public readonly context: ErrorContext;
  public readonly isOperational: boolean;

  constructor(
    code: ErrorCode,
    message: string,
    details?: Record<string, any>,
    userFriendlyMessage?: string,
    isOperational: boolean = true
  ) {
    super(message);
    this.name = 'ForensicsError';
    this.code = code;
    this.isOperational = isOperational;

    this.context = {
      code,
      message,
      details,
      timestamp: new Date().toISOString(),
      stack: this.stack,
      userFriendlyMessage: userFriendlyMessage || this.getDefaultUserMessage(code),
      recoverable: this.isRecoverable(code),
      retryable: this.isRetryable(code),
    };

    // Capture stack trace
    Error.captureStackTrace(this, ForensicsError);
  }

  private getDefaultUserMessage(code: ErrorCode): string {
    const userMessages: Record<ErrorCode, string> = {
      [ErrorCode.INVALID_BITCOIN_ADDRESS]:
        'The Bitcoin address you provided is not valid. Please check and try again.',
      [ErrorCode.INVALID_TRANSACTION_ID]:
        'The transaction ID you provided is not valid. Please check and try again.',
      [ErrorCode.INVALID_INPUT_PARAMETERS]:
        'Some of the information you provided is not valid. Please check your inputs.',
      [ErrorCode.API_REQUEST_FAILED]:
        'We encountered an issue connecting to the Bitcoin network. Please try again later.',
      [ErrorCode.API_RATE_LIMITED]:
        'We are making too many requests. Please wait a moment and try again.',
      [ErrorCode.API_TIMEOUT]: 'The request took too long to complete. Please try again.',
      [ErrorCode.API_UNAUTHORIZED]:
        'Access to the Bitcoin network was denied. Please check your configuration.',
      [ErrorCode.INVESTIGATION_FAILED]:
        'The investigation could not be completed. Please try again or contact support.',
      [ErrorCode.TRANSACTION_NOT_FOUND]:
        'The transaction could not be found on the Bitcoin network.',
      [ErrorCode.ADDRESS_NOT_FOUND]:
        'The Bitcoin address could not be found or has no transaction history.',
      [ErrorCode.INSUFFICIENT_DATA]: 'Not enough data was found to complete the investigation.',
      [ErrorCode.FILE_NOT_FOUND]: 'A required file could not be found.',
      [ErrorCode.FILE_WRITE_ERROR]:
        'Could not save the investigation results. Please check file permissions.',
      [ErrorCode.FILE_READ_ERROR]: 'Could not read a required file. Please check file permissions.',
      [ErrorCode.DIRECTORY_CREATE_ERROR]:
        'Could not create the output directory. Please check permissions.',
      [ErrorCode.SECURITY_VIOLATION]:
        'A security issue was detected. The operation has been blocked.',
      [ErrorCode.UNAUTHORIZED_ACCESS]: 'You do not have permission to perform this operation.',
      [ErrorCode.DATA_INTEGRITY_ERROR]:
        'Data integrity check failed. The data may have been corrupted.',
      [ErrorCode.MEMORY_LIMIT_EXCEEDED]:
        'The investigation requires too much memory. Please try with smaller parameters.',
      [ErrorCode.TIMEOUT_ERROR]: 'The operation took too long to complete. Please try again.',
      [ErrorCode.CONFIGURATION_ERROR]:
        'There is an issue with the tool configuration. Please check your settings.',
      [ErrorCode.UNKNOWN_ERROR]:
        'An unexpected error occurred. Please try again or contact support.',
    };

    return (
      userMessages[code] || 'An unexpected error occurred. Please try again or contact support.'
    );
  }

  private isRecoverable(code: ErrorCode): boolean {
    const recoverableErrors = [
      ErrorCode.API_RATE_LIMITED,
      ErrorCode.API_TIMEOUT,
      ErrorCode.FILE_WRITE_ERROR,
      ErrorCode.DIRECTORY_CREATE_ERROR,
      ErrorCode.TIMEOUT_ERROR,
    ];
    return recoverableErrors.includes(code);
  }

  private isRetryable(code: ErrorCode): boolean {
    const retryableErrors = [
      ErrorCode.API_REQUEST_FAILED,
      ErrorCode.API_RATE_LIMITED,
      ErrorCode.API_TIMEOUT,
      ErrorCode.TIMEOUT_ERROR,
    ];
    return retryableErrors.includes(code);
  }
}

export class ErrorHandler {
  private static instance: ErrorHandler;
  private errorCounts: Map<ErrorCode, number> = new Map();
  private lastErrors: Map<ErrorCode, Date> = new Map();

  public static getInstance(): ErrorHandler {
    if (!ErrorHandler.instance) {
      ErrorHandler.instance = new ErrorHandler();
    }
    return ErrorHandler.instance;
  }

  /**
   * Handle and log errors with appropriate context
   */
  public handleError(error: Error | ForensicsError, context?: Record<string, any>): ErrorContext {
    let errorContext: ErrorContext;

    if (error instanceof ForensicsError) {
      errorContext = error.context;
    } else {
      // Convert regular errors to ForensicsError
      const forensicsError = this.convertToForensicsError(error);
      errorContext = forensicsError.context;
    }

    // Add additional context
    if (context) {
      errorContext.details = { ...errorContext.details, ...context };
    }

    // Track error frequency
    this.trackError(errorContext.code);

    // Log the error
    this.logError(errorContext);

    return errorContext;
  }

  /**
   * Convert regular errors to ForensicsError
   */
  private convertToForensicsError(error: Error): ForensicsError {
    let code = ErrorCode.UNKNOWN_ERROR;
    let isOperational = false;

    // Try to determine error type from message or properties
    if (error.message.includes('ENOENT') || error.message.includes('file not found')) {
      code = ErrorCode.FILE_NOT_FOUND;
      isOperational = true;
    } else if (error.message.includes('EACCES') || error.message.includes('permission denied')) {
      code = ErrorCode.UNAUTHORIZED_ACCESS;
      isOperational = true;
    } else if (error.message.includes('timeout')) {
      code = ErrorCode.TIMEOUT_ERROR;
      isOperational = true;
    } else if (error.message.includes('network') || error.message.includes('ECONNREFUSED')) {
      code = ErrorCode.API_REQUEST_FAILED;
      isOperational = true;
    }

    return new ForensicsError(
      code,
      error.message,
      { originalError: error.name, stack: error.stack },
      undefined,
      isOperational
    );
  }

  /**
   * Track error frequency for monitoring
   */
  private trackError(code: ErrorCode): void {
    const count = this.errorCounts.get(code) || 0;
    this.errorCounts.set(code, count + 1);
    this.lastErrors.set(code, new Date());
  }

  /**
   * Log error with appropriate level
   */
  private logError(errorContext: ErrorContext): void {
    const logData = {
      code: errorContext.code,
      message: errorContext.message,
      details: errorContext.details,
      timestamp: errorContext.timestamp,
      recoverable: errorContext.recoverable,
      retryable: errorContext.retryable,
    };

    if (errorContext.recoverable) {
      logger.warn('Recoverable error occurred', logData);
    } else {
      logger.error('Error occurred', logData);
    }
  }

  /**
   * Get error statistics for monitoring
   */
  public getErrorStats(): {
    totalErrors: number;
    errorsByCode: Record<string, number>;
    recentErrors: Record<string, string>;
  } {
    const totalErrors = Array.from(this.errorCounts.values()).reduce(
      (sum, count) => sum + count,
      0
    );

    const errorsByCode: Record<string, number> = {};
    this.errorCounts.forEach((count, code) => {
      errorsByCode[code] = count;
    });

    const recentErrors: Record<string, string> = {};
    this.lastErrors.forEach((date, code) => {
      recentErrors[code] = date.toISOString();
    });

    return {
      totalErrors,
      errorsByCode,
      recentErrors,
    };
  }

  /**
   * Reset error statistics
   */
  public resetStats(): void {
    this.errorCounts.clear();
    this.lastErrors.clear();
  }

  /**
   * Check if an error should trigger an alert
   */
  public shouldAlert(code: ErrorCode): boolean {
    const count = this.errorCounts.get(code) || 0;
    const lastError = this.lastErrors.get(code);

    // Alert thresholds
    const ALERT_THRESHOLDS: Record<ErrorCode, number> = {
      [ErrorCode.API_REQUEST_FAILED]: 5,
      [ErrorCode.SECURITY_VIOLATION]: 1,
      [ErrorCode.DATA_INTEGRITY_ERROR]: 1,
      [ErrorCode.MEMORY_LIMIT_EXCEEDED]: 3,
      [ErrorCode.UNKNOWN_ERROR]: 10,
    } as any;

    const threshold = ALERT_THRESHOLDS[code] || 20;

    // Alert if we've exceeded the threshold in the last hour
    if (count >= threshold && lastError) {
      const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
      return lastError > oneHourAgo;
    }

    return false;
  }
}

/**
 * Global error handler for uncaught exceptions
 */
export function setupGlobalErrorHandling(): void {
  const errorHandler = ErrorHandler.getInstance();

  process.on('uncaughtException', (error: Error) => {
    const context = errorHandler.handleError(error, { source: 'uncaughtException' });

    console.error('Uncaught Exception:', context.userFriendlyMessage);

    // Exit gracefully for non-operational errors
    if (!context.recoverable) {
      process.exit(1);
    }
  });

  process.on('unhandledRejection', (reason: any, promise: Promise<any>) => {
    const error = reason instanceof Error ? reason : new Error(String(reason));
    const context = errorHandler.handleError(error, {
      source: 'unhandledRejection',
      promise: promise.toString(),
    });

    console.error('Unhandled Rejection:', context.userFriendlyMessage);

    // Exit gracefully for non-operational errors
    if (!context.recoverable) {
      process.exit(1);
    }
  });
}

/**
 * Utility functions for common error scenarios
 */
export const ErrorUtils = {
  /**
   * Create a validation error
   */
  validation: (message: string, details?: Record<string, any>) =>
    new ForensicsError(ErrorCode.INVALID_INPUT_PARAMETERS, message, details),

  /**
   * Create an API error
   */
  api: (message: string, details?: Record<string, any>) =>
    new ForensicsError(ErrorCode.API_REQUEST_FAILED, message, details),

  /**
   * Create a file system error
   */
  fileSystem: (message: string, details?: Record<string, any>) =>
    new ForensicsError(ErrorCode.FILE_WRITE_ERROR, message, details),

  /**
   * Create a security error
   */
  security: (message: string, details?: Record<string, any>) =>
    new ForensicsError(ErrorCode.SECURITY_VIOLATION, message, details),

  /**
   * Wrap async functions with error handling
   */
  wrapAsync: <T extends any[], R>(
    fn: (...args: T) => Promise<R>,
    errorCode: ErrorCode = ErrorCode.UNKNOWN_ERROR
  ) => {
    return async (...args: T): Promise<R> => {
      try {
        return await fn(...args);
      } catch (error) {
        if (error instanceof ForensicsError) {
          throw error;
        }
        throw new ForensicsError(
          errorCode,
          error instanceof Error ? error.message : String(error),
          { originalError: error }
        );
      }
    };
  },
};
