import crypto from 'crypto';
import path from 'path';
import { ErrorCode, ForensicsError } from './error-handler';
import { logger } from './logger';

export interface SecurityConfig {
  maxFileSize: number; // in bytes
  allowedFileExtensions: string[];
  maxInputLength: number;
  rateLimitWindow: number; // in milliseconds
  maxRequestsPerWindow: number;
  enableInputSanitization: boolean;
  enablePathTraversal: boolean;
}

export const DEFAULT_SECURITY_CONFIG: SecurityConfig = {
  maxFileSize: 100 * 1024 * 1024, // 100MB
  allowedFileExtensions: ['.json', '.txt', '.html', '.pdf', '.csv'],
  maxInputLength: 10000,
  rateLimitWindow: 60 * 1000, // 1 minute
  maxRequestsPerWindow: 100,
  enableInputSanitization: true,
  enablePathTraversal: true,
};

export class SecurityValidator {
  private config: SecurityConfig;
  private requestCounts: Map<string, { count: number; windowStart: number }> = new Map();

  constructor(config: SecurityConfig = DEFAULT_SECURITY_CONFIG) {
    this.config = config;
  }

  /**
   * Validate and sanitize Bitcoin address input
   */
  validateBitcoinAddress(address: string): string {
    if (!address || typeof address !== 'string') {
      throw new ForensicsError(
        ErrorCode.INVALID_BITCOIN_ADDRESS,
        'Bitcoin address must be a non-empty string'
      );
    }

    // Remove whitespace and convert to lowercase for validation
    const cleanAddress = address.trim();

    if (cleanAddress.length === 0) {
      throw new ForensicsError(
        ErrorCode.INVALID_BITCOIN_ADDRESS,
        'Bitcoin address cannot be empty'
      );
    }

    if (cleanAddress.length > 100) {
      throw new ForensicsError(ErrorCode.INVALID_BITCOIN_ADDRESS, 'Bitcoin address is too long');
    }

    // Check for suspicious characters
    if (!/^[a-zA-Z0-9]+$/.test(cleanAddress)) {
      throw new ForensicsError(
        ErrorCode.INVALID_BITCOIN_ADDRESS,
        'Bitcoin address contains invalid characters'
      );
    }

    return cleanAddress;
  }

  /**
   * Validate and sanitize transaction ID input
   */
  validateTransactionId(txid: string): string {
    if (!txid || typeof txid !== 'string') {
      throw new ForensicsError(
        ErrorCode.INVALID_TRANSACTION_ID,
        'Transaction ID must be a non-empty string'
      );
    }

    const cleanTxid = txid.trim().toLowerCase();

    if (cleanTxid.length !== 64) {
      throw new ForensicsError(
        ErrorCode.INVALID_TRANSACTION_ID,
        'Transaction ID must be exactly 64 characters'
      );
    }

    if (!/^[a-f0-9]{64}$/.test(cleanTxid)) {
      throw new ForensicsError(
        ErrorCode.INVALID_TRANSACTION_ID,
        'Transaction ID must contain only hexadecimal characters'
      );
    }

    return cleanTxid;
  }

  /**
   * Validate and sanitize general string input
   */
  validateStringInput(input: string, fieldName: string, maxLength?: number): string {
    if (typeof input !== 'string') {
      throw new ForensicsError(ErrorCode.INVALID_INPUT_PARAMETERS, `${fieldName} must be a string`);
    }

    const maxLen = maxLength || this.config.maxInputLength;
    if (input.length > maxLen) {
      throw new ForensicsError(
        ErrorCode.INVALID_INPUT_PARAMETERS,
        `${fieldName} exceeds maximum length of ${maxLen} characters`
      );
    }

    if (this.config.enableInputSanitization) {
      return this.sanitizeString(input);
    }

    return input;
  }

  /**
   * Validate numeric input
   */
  validateNumericInput(input: any, fieldName: string, min?: number, max?: number): number {
    const num = Number(input);

    if (isNaN(num) || !isFinite(num)) {
      throw new ForensicsError(
        ErrorCode.INVALID_INPUT_PARAMETERS,
        `${fieldName} must be a valid number`
      );
    }

    if (min !== undefined && num < min) {
      throw new ForensicsError(
        ErrorCode.INVALID_INPUT_PARAMETERS,
        `${fieldName} must be at least ${min}`
      );
    }

    if (max !== undefined && num > max) {
      throw new ForensicsError(
        ErrorCode.INVALID_INPUT_PARAMETERS,
        `${fieldName} must be at most ${max}`
      );
    }

    return num;
  }

  /**
   * Validate file path for security
   */
  validateFilePath(filePath: string, baseDirectory?: string): string {
    if (!filePath || typeof filePath !== 'string') {
      throw new ForensicsError(
        ErrorCode.INVALID_INPUT_PARAMETERS,
        'File path must be a non-empty string'
      );
    }

    const cleanPath = path.normalize(filePath);

    // Check for path traversal attempts
    if (this.config.enablePathTraversal && cleanPath.includes('..')) {
      throw new ForensicsError(
        ErrorCode.SECURITY_VIOLATION,
        'Path traversal detected in file path'
      );
    }

    // Validate file extension
    const ext = path.extname(cleanPath).toLowerCase();
    if (ext && !this.config.allowedFileExtensions.includes(ext)) {
      throw new ForensicsError(
        ErrorCode.SECURITY_VIOLATION,
        `File extension ${ext} is not allowed`
      );
    }

    // Ensure path is within base directory if specified
    if (baseDirectory) {
      const resolvedPath = path.resolve(cleanPath);
      const resolvedBase = path.resolve(baseDirectory);

      if (!resolvedPath.startsWith(resolvedBase)) {
        throw new ForensicsError(
          ErrorCode.SECURITY_VIOLATION,
          'File path is outside allowed directory'
        );
      }
    }

    return cleanPath;
  }

  /**
   * Rate limiting check
   */
  checkRateLimit(identifier: string): boolean {
    const now = Date.now();
    const record = this.requestCounts.get(identifier);

    if (!record) {
      this.requestCounts.set(identifier, { count: 1, windowStart: now });
      return true;
    }

    // Check if we're in a new window
    if (now - record.windowStart > this.config.rateLimitWindow) {
      this.requestCounts.set(identifier, { count: 1, windowStart: now });
      return true;
    }

    // Check if we've exceeded the limit
    if (record.count >= this.config.maxRequestsPerWindow) {
      logger.warn('Rate limit exceeded', { identifier, count: record.count });
      return false;
    }

    // Increment count
    record.count++;
    return true;
  }

  /**
   * Sanitize string input
   */
  private sanitizeString(input: string): string {
    return input
      .replace(/[<>]/g, '') // Remove potential HTML tags
      .replace(/['"]/g, '') // Remove quotes
      .replace(/[\\]/g, '') // Remove backslashes
      .replace(/[\x00-\x1f\x7f]/g, '') // Remove control characters
      .trim();
  }

  /**
   * Generate secure hash for data integrity
   */
  generateHash(data: string | Buffer): string {
    return crypto.createHash('sha256').update(data).digest('hex');
  }

  /**
   * Verify data integrity using hash
   */
  verifyHash(data: string | Buffer, expectedHash: string): boolean {
    const actualHash = this.generateHash(data);
    return actualHash === expectedHash;
  }

  /**
   * Generate secure random ID
   */
  generateSecureId(length: number = 32): string {
    return crypto.randomBytes(length).toString('hex');
  }

  /**
   * Validate file size
   */
  validateFileSize(size: number): void {
    if (size > this.config.maxFileSize) {
      throw new ForensicsError(
        ErrorCode.SECURITY_VIOLATION,
        `File size ${size} bytes exceeds maximum allowed size of ${this.config.maxFileSize} bytes`
      );
    }
  }

  /**
   * Clean up rate limit records
   */
  cleanupRateLimitRecords(): void {
    const now = Date.now();
    const cutoff = now - this.config.rateLimitWindow;

    for (const [identifier, record] of this.requestCounts.entries()) {
      if (record.windowStart < cutoff) {
        this.requestCounts.delete(identifier);
      }
    }
  }

  /**
   * Get security statistics
   */
  getSecurityStats(): {
    activeRateLimits: number;
    totalRequests: number;
    blockedRequests: number;
  } {
    let totalRequests = 0;
    let blockedRequests = 0;

    for (const record of this.requestCounts.values()) {
      totalRequests += record.count;
      if (record.count >= this.config.maxRequestsPerWindow) {
        blockedRequests++;
      }
    }

    return {
      activeRateLimits: this.requestCounts.size,
      totalRequests,
      blockedRequests,
    };
  }
}

/**
 * Input validation decorators and utilities
 */
export class InputValidator {
  private static securityValidator = new SecurityValidator();

  /**
   * Validate investigation parameters
   */
  static validateInvestigationParams(params: {
    initialTxid?: string;
    targetAddress?: string;
    maxDepth?: number;
    victimName?: string;
    caseDescription?: string;
  }): void {
    if (params.initialTxid) {
      this.securityValidator.validateTransactionId(params.initialTxid);
    }

    if (params.targetAddress) {
      this.securityValidator.validateBitcoinAddress(params.targetAddress);
    }

    if (params.maxDepth !== undefined) {
      this.securityValidator.validateNumericInput(params.maxDepth, 'maxDepth', 1, 20);
    }

    if (params.victimName) {
      this.securityValidator.validateStringInput(params.victimName, 'victimName', 100);
    }

    if (params.caseDescription) {
      this.securityValidator.validateStringInput(params.caseDescription, 'caseDescription', 1000);
    }
  }

  /**
   * Validate API configuration
   */
  static validateApiConfig(config: {
    apiBaseUrl?: string;
    requestTimeout?: number;
    rateLimitDelay?: number;
    maxRetries?: number;
  }): void {
    if (config.apiBaseUrl) {
      if (!config.apiBaseUrl.startsWith('https://')) {
        throw new ForensicsError(ErrorCode.SECURITY_VIOLATION, 'API base URL must use HTTPS');
      }
    }

    if (config.requestTimeout !== undefined) {
      this.securityValidator.validateNumericInput(
        config.requestTimeout,
        'requestTimeout',
        1000,
        300000
      );
    }

    if (config.rateLimitDelay !== undefined) {
      this.securityValidator.validateNumericInput(
        config.rateLimitDelay,
        'rateLimitDelay',
        0,
        10000
      );
    }

    if (config.maxRetries !== undefined) {
      this.securityValidator.validateNumericInput(config.maxRetries, 'maxRetries', 0, 10);
    }
  }
}

/**
 * Security middleware for API requests
 */
export class SecurityMiddleware {
  private static validator = new SecurityValidator();

  /**
   * Apply security checks to API requests
   */
  static async secureApiRequest<T>(
    requestFn: () => Promise<T>,
    identifier: string,
    context?: Record<string, any>
  ): Promise<T> {
    // Rate limiting check
    if (!this.validator.checkRateLimit(identifier)) {
      throw new ForensicsError(ErrorCode.API_RATE_LIMITED, 'Rate limit exceeded for API requests', {
        identifier,
        context,
      });
    }

    try {
      const result = await requestFn();
      return result;
    } catch (error) {
      logger.warn('Secured API request failed', {
        identifier,
        error: error instanceof Error ? error.message : String(error),
        context,
      });
      throw error;
    }
  }

  /**
   * Periodic cleanup of security records
   */
  static startCleanupTimer(): NodeJS.Timeout {
    return setInterval(
      () => {
        this.validator.cleanupRateLimitRecords();
      },
      5 * 60 * 1000
    ); // Clean up every 5 minutes
  }
}

/**
 * Export singleton instance
 */
export const securityValidator = new SecurityValidator();
