import os from 'os';
import fs from 'fs';
import { performance } from 'perf_hooks';
import { logger } from './logger';
import { <PERSON>rro<PERSON><PERSON><PERSON><PERSON> } from './error-handler';

export interface HealthStatus {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: string;
  uptime: number;
  version: string;
  checks: {
    memory: HealthCheck;
    disk: HealthCheck;
    api: HealthCheck;
    errors: HealthCheck;
  };
  metrics: SystemMetrics;
}

export interface HealthCheck {
  status: 'pass' | 'warn' | 'fail';
  message: string;
  value?: number;
  threshold?: number;
  lastChecked: string;
}

export interface SystemMetrics {
  memory: {
    used: number;
    total: number;
    percentage: number;
    heapUsed: number;
    heapTotal: number;
  };
  cpu: {
    usage: number;
    loadAverage: number[];
  };
  disk: {
    used: number;
    available: number;
    percentage: number;
  };
  network: {
    requestCount: number;
    errorCount: number;
    averageResponseTime: number;
  };
  investigations: {
    total: number;
    successful: number;
    failed: number;
    averageDuration: number;
  };
}

export interface PerformanceMetrics {
  operationName: string;
  duration: number;
  timestamp: string;
  success: boolean;
  metadata?: Record<string, any>;
}

export class MonitoringService {
  private static instance: MonitoringService;
  private startTime: number;
  private performanceMetrics: PerformanceMetrics[] = [];
  private investigationMetrics: {
    total: number;
    successful: number;
    failed: number;
    totalDuration: number;
  } = {
    total: 0,
    successful: 0,
    failed: 0,
    totalDuration: 0,
  };
  private networkMetrics: {
    requestCount: number;
    errorCount: number;
    totalResponseTime: number;
  } = {
    requestCount: 0,
    errorCount: 0,
    totalResponseTime: 0,
  };

  private constructor() {
    this.startTime = Date.now();
    this.startPeriodicHealthChecks();
  }

  public static getInstance(): MonitoringService {
    if (!MonitoringService.instance) {
      MonitoringService.instance = new MonitoringService();
    }
    return MonitoringService.instance;
  }

  /**
   * Get comprehensive health status
   */
  public async getHealthStatus(): Promise<HealthStatus> {
    const timestamp = new Date().toISOString();
    const uptime = Date.now() - this.startTime;

    const checks = {
      memory: await this.checkMemoryHealth(),
      disk: await this.checkDiskHealth(),
      api: await this.checkApiHealth(),
      errors: await this.checkErrorHealth(),
    };

    const metrics = await this.getSystemMetrics();

    // Determine overall status
    const failedChecks = Object.values(checks).filter(check => check.status === 'fail').length;
    const warnChecks = Object.values(checks).filter(check => check.status === 'warn').length;

    let status: 'healthy' | 'degraded' | 'unhealthy';
    if (failedChecks > 0) {
      status = 'unhealthy';
    } else if (warnChecks > 0) {
      status = 'degraded';
    } else {
      status = 'healthy';
    }

    return {
      status,
      timestamp,
      uptime,
      version: '3.0.0',
      checks,
      metrics,
    };
  }

  /**
   * Record performance metrics
   */
  public recordPerformance(
    operationName: string,
    duration: number,
    success: boolean,
    metadata?: Record<string, any>
  ): void {
    const metric: PerformanceMetrics = {
      operationName,
      duration,
      timestamp: new Date().toISOString(),
      success,
      metadata,
    };

    this.performanceMetrics.push(metric);

    // Keep only last 1000 metrics
    if (this.performanceMetrics.length > 1000) {
      this.performanceMetrics = this.performanceMetrics.slice(-1000);
    }

    // Log slow operations
    if (duration > 10000) {
      // 10 seconds
      logger.warn('Slow operation detected', {
        operationName,
        duration,
        success,
        metadata,
      });
    }
  }

  /**
   * Record investigation metrics
   */
  public recordInvestigation(success: boolean, duration: number): void {
    this.investigationMetrics.total++;
    this.investigationMetrics.totalDuration += duration;

    if (success) {
      this.investigationMetrics.successful++;
    } else {
      this.investigationMetrics.failed++;
    }
  }

  /**
   * Record network request metrics
   */
  public recordNetworkRequest(success: boolean, responseTime: number): void {
    this.networkMetrics.requestCount++;
    this.networkMetrics.totalResponseTime += responseTime;

    if (!success) {
      this.networkMetrics.errorCount++;
    }
  }

  /**
   * Get performance statistics
   */
  public getPerformanceStats(): {
    totalOperations: number;
    averageDuration: number;
    successRate: number;
    slowOperations: number;
    operationBreakdown: Record<string, { count: number; avgDuration: number; successRate: number }>;
  } {
    if (this.performanceMetrics.length === 0) {
      return {
        totalOperations: 0,
        averageDuration: 0,
        successRate: 0,
        slowOperations: 0,
        operationBreakdown: {},
      };
    }

    const totalOperations = this.performanceMetrics.length;
    const totalDuration = this.performanceMetrics.reduce((sum, m) => sum + m.duration, 0);
    const successfulOperations = this.performanceMetrics.filter(m => m.success).length;
    const slowOperations = this.performanceMetrics.filter(m => m.duration > 10000).length;

    const operationBreakdown: Record<
      string,
      { count: number; avgDuration: number; successRate: number }
    > = {};

    for (const metric of this.performanceMetrics) {
      if (!operationBreakdown[metric.operationName]) {
        operationBreakdown[metric.operationName] = { count: 0, avgDuration: 0, successRate: 0 };
      }

      const breakdown = operationBreakdown[metric.operationName];
      breakdown.count++;
      breakdown.avgDuration =
        (breakdown.avgDuration * (breakdown.count - 1) + metric.duration) / breakdown.count;
      breakdown.successRate =
        this.performanceMetrics.filter(m => m.operationName === metric.operationName && m.success)
          .length / breakdown.count;
    }

    return {
      totalOperations,
      averageDuration: totalDuration / totalOperations,
      successRate: successfulOperations / totalOperations,
      slowOperations,
      operationBreakdown,
    };
  }

  /**
   * Performance timing decorator
   */
  public timeOperation<T>(operationName: string, metadata?: Record<string, any>) {
    return (target: any, propertyName: string, descriptor: PropertyDescriptor) => {
      const method = descriptor.value;

      descriptor.value = async function (...args: any[]): Promise<T> {
        const startTime = performance.now();
        let success = true;
        let result: T;

        try {
          result = await method.apply(this, args);
          return result;
        } catch (error) {
          success = false;
          throw error;
        } finally {
          const duration = performance.now() - startTime;
          MonitoringService.getInstance().recordPerformance(
            operationName,
            duration,
            success,
            metadata
          );
        }
      };
    };
  }

  /**
   * Check memory health
   */
  private async checkMemoryHealth(): Promise<HealthCheck> {
    const memUsage = process.memoryUsage();
    const totalMem = os.totalmem();
    const freeMem = os.freemem();
    const usedMem = totalMem - freeMem;
    const memPercentage = (usedMem / totalMem) * 100;

    const heapPercentage = (memUsage.heapUsed / memUsage.heapTotal) * 100;

    let status: 'pass' | 'warn' | 'fail' = 'pass';
    let message = 'Memory usage is normal';

    if (memPercentage > 90 || heapPercentage > 90) {
      status = 'fail';
      message = 'Critical memory usage detected';
    } else if (memPercentage > 80 || heapPercentage > 80) {
      status = 'warn';
      message = 'High memory usage detected';
    }

    return {
      status,
      message,
      value: Math.max(memPercentage, heapPercentage),
      threshold: 80,
      lastChecked: new Date().toISOString(),
    };
  }

  /**
   * Check disk health
   */
  private async checkDiskHealth(): Promise<HealthCheck> {
    try {
      const stats = fs.statSync(process.cwd());
      // This is a simplified check - in production, you'd want to check actual disk usage

      return {
        status: 'pass',
        message: 'Disk access is normal',
        lastChecked: new Date().toISOString(),
      };
    } catch (error) {
      return {
        status: 'fail',
        message: 'Disk access error detected',
        lastChecked: new Date().toISOString(),
      };
    }
  }

  /**
   * Check API health
   */
  private async checkApiHealth(): Promise<HealthCheck> {
    const errorRate =
      this.networkMetrics.requestCount > 0
        ? (this.networkMetrics.errorCount / this.networkMetrics.requestCount) * 100
        : 0;

    const avgResponseTime =
      this.networkMetrics.requestCount > 0
        ? this.networkMetrics.totalResponseTime / this.networkMetrics.requestCount
        : 0;

    let status: 'pass' | 'warn' | 'fail' = 'pass';
    let message = 'API performance is normal';

    if (errorRate > 50 || avgResponseTime > 30000) {
      status = 'fail';
      message = 'Critical API performance issues detected';
    } else if (errorRate > 20 || avgResponseTime > 10000) {
      status = 'warn';
      message = 'API performance degradation detected';
    }

    return {
      status,
      message,
      value: Math.max(errorRate, avgResponseTime / 1000),
      threshold: 20,
      lastChecked: new Date().toISOString(),
    };
  }

  /**
   * Check error health
   */
  private async checkErrorHealth(): Promise<HealthCheck> {
    const errorHandler = ErrorHandler.getInstance();
    const errorStats = errorHandler.getErrorStats();

    const recentErrorCount = Object.values(errorStats.recentErrors).filter(timestamp => {
      const errorTime = new Date(timestamp).getTime();
      const oneHourAgo = Date.now() - 60 * 60 * 1000;
      return errorTime > oneHourAgo;
    }).length;

    let status: 'pass' | 'warn' | 'fail' = 'pass';
    let message = 'Error rate is normal';

    if (recentErrorCount > 50) {
      status = 'fail';
      message = 'Critical error rate detected';
    } else if (recentErrorCount > 20) {
      status = 'warn';
      message = 'Elevated error rate detected';
    }

    return {
      status,
      message,
      value: recentErrorCount,
      threshold: 20,
      lastChecked: new Date().toISOString(),
    };
  }

  /**
   * Get system metrics
   */
  private async getSystemMetrics(): Promise<SystemMetrics> {
    const memUsage = process.memoryUsage();
    const totalMem = os.totalmem();
    const freeMem = os.freemem();
    const usedMem = totalMem - freeMem;

    const cpuUsage = process.cpuUsage();
    const loadAverage = os.loadavg();

    return {
      memory: {
        used: usedMem,
        total: totalMem,
        percentage: (usedMem / totalMem) * 100,
        heapUsed: memUsage.heapUsed,
        heapTotal: memUsage.heapTotal,
      },
      cpu: {
        usage: (cpuUsage.user + cpuUsage.system) / 1000000, // Convert to seconds
        loadAverage,
      },
      disk: {
        used: 0, // Would implement actual disk usage check
        available: 0,
        percentage: 0,
      },
      network: {
        requestCount: this.networkMetrics.requestCount,
        errorCount: this.networkMetrics.errorCount,
        averageResponseTime:
          this.networkMetrics.requestCount > 0
            ? this.networkMetrics.totalResponseTime / this.networkMetrics.requestCount
            : 0,
      },
      investigations: {
        total: this.investigationMetrics.total,
        successful: this.investigationMetrics.successful,
        failed: this.investigationMetrics.failed,
        averageDuration:
          this.investigationMetrics.total > 0
            ? this.investigationMetrics.totalDuration / this.investigationMetrics.total
            : 0,
      },
    };
  }

  /**
   * Start periodic health checks
   */
  private startPeriodicHealthChecks(): void {
    // Log health status every 5 minutes
    setInterval(
      async () => {
        try {
          const health = await this.getHealthStatus();

          if (health.status !== 'healthy') {
            logger.warn('System health check', {
              status: health.status,
              failedChecks: Object.entries(health.checks)
                .filter(([_, check]) => check.status === 'fail')
                .map(([name, check]) => ({ name, message: check.message })),
            });
          } else {
            logger.info('System health check passed', {
              uptime: health.uptime,
              memoryUsage: health.metrics.memory.percentage,
            });
          }
        } catch (error) {
          logger.error('Health check failed', {
            error: error instanceof Error ? error.message : String(error),
          });
        }
      },
      5 * 60 * 1000
    ); // 5 minutes

    // Clean up old performance metrics every hour
    setInterval(
      () => {
        const oneHourAgo = Date.now() - 60 * 60 * 1000;
        this.performanceMetrics = this.performanceMetrics.filter(
          metric => new Date(metric.timestamp).getTime() > oneHourAgo
        );
      },
      60 * 60 * 1000
    ); // 1 hour
  }
}

/**
 * Export singleton instance
 */
export const monitoring = MonitoringService.getInstance();
