
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bitcoin Network Graph - 53c45fe1-5aed-4f9b-894c-4d51ae5d898f</title>
    <script src="https://cdn.jsdelivr.net/npm/vis-network@latest/dist/vis-network.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
        #networkGraph { height: 80vh; border: 1px solid #ccc; }
        .controls { margin-bottom: 20px; }
        .legend { margin-top: 20px; display: flex; gap: 20px; }
        .legend-item { display: flex; align-items: center; gap: 5px; }
        .legend-color { width: 20px; height: 20px; border-radius: 50%; }
    </style>
</head>
<body>
    <h1>Bitcoin Transaction Network Graph</h1>
    <div class="controls">
        <button onclick="fitNetwork()">Fit to Screen</button>
        <button onclick="togglePhysics()">Toggle Physics</button>
        <label>
            <input type="checkbox" id="showLabels" checked onchange="toggleLabels()">
            Show Labels
        </label>
    </div>

    <div id="networkGraph"></div>

    <div class="legend">
        <div class="legend-item">
            <div class="legend-color" style="background: #97c2fc;"></div>
            <span>Regular Address</span>
        </div>
        <div class="legend-item">
            <div class="legend-color" style="background: #fb7e81;"></div>
            <span>High Risk Address</span>
        </div>
        <div class="legend-item">
            <div class="legend-color" style="background: #ffa500;"></div>
            <span>Exchange</span>
        </div>
        <div class="legend-item">
            <div class="legend-color" style="background: #7be141;"></div>
            <span>Mixer Service</span>
        </div>
    </div>

    <script>
        let network;
        let physicsEnabled = true;

        function initNetwork() {
            const container = document.getElementById('networkGraph');

            // Prepare nodes with colors based on type and risk
            const nodes = [{"id":"1A1zP1eP5QGefi2DMPTfTL5SLmv7DivfNa","label":"1A1zP1eP...v7DivfNa","type":"address","value":0,"level":0,"metadata":{"address":"1A1zP1eP5QGefi2DMPTfTL5SLmv7DivfNa","suspicious":false}}].map(node => ({
                ...node,
                color: getNodeColor(node),
                title: getNodeTooltip(node)
            }));

            const edges = [].map(edge => ({
                ...edge,
                title: getEdgeTooltip(edge),
                color: { color: '#848484' },
                width: Math.max(1, Math.min(10, edge.value * 2))
            }));

            const data = {
                nodes: new vis.DataSet(nodes),
                edges: new vis.DataSet(edges)
            };

            const options = {
                nodes: {
                    shape: 'dot',
                    size: 20,
                    font: { size: 14, color: '#000000' },
                    borderWidth: 2,
                    shadow: true
                },
                edges: {
                    shadow: true,
                    arrows: { to: { enabled: true, scaleFactor: 1 } },
                    smooth: { type: 'continuous' }
                },
                physics: {
                    enabled: true,
                    stabilization: { iterations: 200 },
                    barnesHut: {
                        gravitationalConstant: -8000,
                        centralGravity: 0.3,
                        springLength: 95,
                        springConstant: 0.04,
                        damping: 0.09
                    }
                },
                interaction: {
                    hover: true,
                    tooltipDelay: 200,
                    hideEdgesOnDrag: true
                }
            };

            network = new vis.Network(container, data, options);

            // Add click event listener
            network.on('click', function(params) {
                if (params.nodes.length > 0) {
                    const nodeId = params.nodes[0];
                    const node = nodes.find(n => n.id === nodeId);
                    if (node) {
                        alert('Address: ' + node.metadata.address + '\nType: ' + node.type + '\nValue: ' + node.value + ' BTC');
                    }
                }
            });
        }

        function getNodeColor(node) {
            if (node.type === 'exchange') return '#ffa500';
            if (node.type === 'mixer') return '#7be141';
            if (node.riskScore && node.riskScore > 7) return '#fb7e81';
            return '#97c2fc';
        }

        function getNodeTooltip(node) {
            return 'Address: ' + node.label + '\nType: ' + node.type + '\nValue: ' + node.value.toFixed(4) + ' BTC' +
                   (node.riskScore ? '\nRisk Score: ' + node.riskScore : '');
        }

        function getEdgeTooltip(edge) {
            return 'Transaction: ' + edge.metadata.txid.substring(0, 16) + '...\nAmount: ' + edge.value.toFixed(4) + ' BTC\nTime: ' + new Date(edge.metadata.timestamp).toLocaleString();
        }

        function fitNetwork() {
            network.fit();
        }

        function togglePhysics() {
            physicsEnabled = !physicsEnabled;
            network.setOptions({ physics: { enabled: physicsEnabled } });
        }

        function toggleLabels() {
            const showLabels = document.getElementById('showLabels').checked;
            network.setOptions({
                nodes: {
                    font: { size: showLabels ? 14 : 0 }
                }
            });
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', initNetwork);
    </script>
</body>
</html>