{"metadata": {"investigationId": "53c45fe1-5aed-4f9b-894c-4d51ae5d898f", "generatedAt": "2025-07-20T14:12:52.725Z", "toolVersion": "3.0.0", "format": "json"}, "investigation": {"investigationId": "53c45fe1-5aed-4f9b-894c-4d51ae5d898f", "timestamp": "2025-07-20T14:12:52.661Z", "inputParameters": {"initialTxid": "f4184fc596403b9d638783cf57adfe4c75c605f6356fbc91338530e9831e9e16", "targetAddress": "**********************************", "maxDepth": 5, "victimName": "Test User", "caseDescription": "Test investigation for production verification"}, "basicResults": {"transactionCount": 0, "addressCount": 1, "totalAmount": 0}, "detailedTransactions": [], "addressAnalysis": [{"address": "**********************************", "addressType": "legacy", "totalReceived": 0, "totalSent": 0, "balance": 0, "transactionCount": 0, "firstSeen": "2025-07-20T14:12:50.740Z"}], "advancedAnalysis": {"transactionPatterns": {"timingAnalysis": {"averageIntervalSeconds": 0, "rapidSuccessionCount": 0, "totalIntervals": 0, "suspiciousTiming": false, "timePatterns": []}, "amountAnalysis": {"totalAmount": 0, "averageAmount": 0, "maxAmount": 0, "minAmount": 0, "roundAmountsCount": 0, "similarAmountsCount": 0, "potentialStructuring": false, "potentialSplitting": false}, "addressReuse": {"uniqueFromAddresses": 0, "uniqueToAddresses": 0, "reusedFromAddresses": {}, "reusedToAddresses": {}, "addressReuseDetected": false}, "clusteringHints": {"totalClusters": 0, "potentialClusters": 0, "clusterDetails": {}, "clusteringDetected": false}, "riskIndicators": {"riskScore": 0, "riskLevel": "MINIMAL", "riskFactors": [], "totalAmount": 0, "transactionCount": 0, "maxDepth": 0}}, "suspiciousActivity": {"mixingServices": {"detected": false, "severity": 3, "confidence": 0, "details": {"smallOutputs": 0, "roundAmounts": 0}}, "exchangeDeposits": {"detected": false, "severity": 2, "confidence": 0, "details": {"potentialExchanges": {}, "addressesWithMultipleSources": 0}}, "peelChains": {"detected": false, "severity": 2, "confidence": 0, "details": {"reason": "insufficient_transactions"}}, "consolidationPatterns": {"detected": false, "severity": 1, "confidence": 0, "details": {"consolidationAddresses": {}}}, "privacyCoinInteractions": {"detected": false, "severity": 3, "confidence": 0, "details": {"note": "Cross-chain analysis not yet implemented"}}, "overallSuspicionScore": 0, "suspicionLevel": "MINIMAL"}, "riskAssessment": {"compositeRiskScore": 0.4, "finalRiskLevel": "MINIMAL", "baseRiskScore": 0.2, "suspicionScore": 0, "riskFactors": ["Transaction Volume", "Address Diversity", "Timing Pat<PERSON>s", "Threat Indicators", "Suspicious Activity"], "suspiciousActivities": [], "recommendations": []}}, "investigationSummary": {"status": "completed", "keyMetrics": {"totalTransactions": 0, "totalAmountBtc": 0, "uniqueAddresses": 1, "maximumDepth": 0, "riskLevel": "UNKNOWN"}, "keyConcerns": [], "investigationQuality": {"qualityScore": 0, "qualityLevel": "MEDIUM", "qualityFactors": [], "completenessPercentage": 0}, "nextSteps": [], "timeline": []}, "evidencePackage": [], "auditTrail": [{"timestamp": "2025-07-20T14:12:50.735Z", "investigationId": "53c45fe1-5aed-4f9b-894c-4d51ae5d898f", "action": "investigation_initialized", "details": {"investigationId": "53c45fe1-5aed-4f9b-894c-4d51ae5d898f", "config": {"apiBaseUrl": "https://blockstream.info/api/", "rateLimitDelay": 100, "maxRetries": 3, "requestTimeout": 30000, "maxDepth": 5, "saveReports": true, "saveVisualizations": true, "outputDirectory": "investigation_results", "enableAIInsights": true}}, "userId": "system", "ipAddress": "localhost"}, {"timestamp": "2025-07-20T14:12:50.739Z", "investigationId": "53c45fe1-5aed-4f9b-894c-4d51ae5d898f", "action": "investigation_started", "details": {"initialTxid": "f4184fc596403b9d638783cf57adfe4c75c605f6356fbc91338530e9831e9e16", "targetAddress": "**********************************", "maxDepth": 5, "victimName": "Test User"}, "userId": "system", "ipAddress": "localhost"}, {"timestamp": "2025-07-20T14:12:52.697Z", "investigationId": "53c45fe1-5aed-4f9b-894c-4d51ae5d898f", "action": "investigation_completed", "details": {"transactionCount": 0, "addressCount": 1, "totalAmount": 0}, "userId": "system", "ipAddress": "localhost"}], "enhancedEvidence": {"totalItems": 1, "categories": ["Address Evidence"], "integrityStatus": {"valid": 1, "invalid": 0}, "packagePath": "investigation_results/enhanced_evidence_package_53c45fe1-5aed-4f9b-894c-4d51ae5d898f.json", "auditReport": {"summary": {"totalEvidence": 1, "categoriesUsed": 1, "integrityChecks": 1, "chainOfCustodyEntries": 2, "attachments": 0}, "evidenceItems": [{"id": "57cd1e8c-146e-451b-9671-9b228dec86aa", "type": "address", "description": "Bitcoin address **********************************", "category": "Address Evidence", "timestamp": "2025-07-20T14:12:51.516Z", "integrityValid": true}], "integrityStatus": {"totalChecks": 1, "passedChecks": 1, "failedChecks": 0}, "chainOfCustodyReport": [{"evidenceId": "57cd1e8c-146e-451b-9671-9b228dec86aa", "description": "Bitcoin address **********************************", "category": "Address Evidence", "custodyEntries": 2, "lastAction": {"timestamp": "2025-07-20T14:12:51.517Z", "action": "evidence_collected", "userId": "investigator", "description": "Address evidence collected during investigation"}, "integrityStatus": "VALID"}]}, "victimReportPath": "investigation_results/victim_report_53c45fe1-5aed-4f9b-894c-4d51ae5d898f_20250720_161252.html", "technicalReports": ["investigation_results/technical_report_53c45fe1-5aed-4f9b-894c-4d51ae5d898f_20250720_161252.html", "investigation_results/technical_report_53c45fe1-5aed-4f9b-894c-4d51ae5d898f_20250720_161252.txt", "investigation_results/evidence_package_53c45fe1-5aed-4f9b-894c-4d51ae5d898f.json"]}, "advancedTracking": {"walletClusters": [], "mixingServices": [], "fundFlows": [], "realTimeBalances": [{"address": "**********************************", "balance": 53.94453689, "unconfirmedBalance": 0, "lastUpdated": "2025-07-20T14:12:52.658Z", "transactionCount": 49193, "recentActivity": [{"txid": "dcbf2ac68aa6f479d4201ca959340aefe8395667c84ec1809cda68e56ba043a2", "amount": null, "type": "outgoing", "timestamp": "2025-07-20T06:42:48.000Z", "confirmations": 906335}, {"txid": "885de97017b5bd848f6c9383af456a99a37f285b031f1c72f21f4bb0c3e1110d", "amount": null, "type": "outgoing", "timestamp": "2025-07-20T01:35:39.000Z", "confirmations": 906302}, {"txid": "b2e899a4ef2f2a245e4549dc904c27869f7294e76b908c81a13974865c0d60bc", "amount": null, "type": "outgoing", "timestamp": "2025-07-19T17:37:57.000Z", "confirmations": 906257}, {"txid": "1ae14c20d4837ffb5bf82896e99dac882373452f6005bbc0af4791b069cf3b4d", "amount": null, "type": "outgoing", "timestamp": "2025-07-19T17:37:57.000Z", "confirmations": 906257}, {"txid": "bc4eea705a5513d36ef1e32ba7e7e12fd7e3562da01688c341c4218af5e152be", "amount": null, "type": "outgoing", "timestamp": "2025-07-19T10:05:21.000Z", "confirmations": 906217}, {"txid": "dda25581ac0e0b153a56f3988f1c2dd75d3b30ad898a191e2aeedeec7e6e62a8", "amount": null, "type": "outgoing", "timestamp": "2025-07-18T18:07:16.000Z", "confirmations": 906117}, {"txid": "2a363d1d8ade52019920494415be2edaf4159385e6fc5d68b66eb482da65ae2e", "amount": null, "type": "outgoing", "timestamp": "2025-07-17T20:21:22.000Z", "confirmations": 905991}, {"txid": "8c80aaed20bb8941173d4e00639fad2d34361afa186a8b99f0cef3e91d1231e7", "amount": null, "type": "outgoing", "timestamp": "2025-07-17T04:27:56.000Z", "confirmations": 905899}, {"txid": "e9681f3c19e886d42af73b9560e84e1f408f49b667f0ba4d394fe81437a32b1b", "amount": null, "type": "outgoing", "timestamp": "2025-07-17T04:02:41.000Z", "confirmations": 905894}, {"txid": "906b234bf561007821530c704be5be8f9cb0e8efc37e7d5cccbb4ea9181469ce", "amount": null, "type": "outgoing", "timestamp": "2025-07-17T02:55:53.000Z", "confirmations": 905886}]}], "complexPatterns": {"peelChains": [], "consolidationEvents": [], "splitTransactions": [], "circularTransactions": []}, "exchangeInteractions": {"knownExchanges": [], "suspectedExchanges": [], "depositAddresses": [], "withdrawalPatterns": []}}, "enhancedRiskAssessment": {"compositeRiskScore": 0.4, "finalRiskLevel": "MINIMAL", "baseRiskScore": 0.2, "suspicionScore": 0, "riskFactorNames": ["Transaction Volume", "Address Diversity", "Timing Pat<PERSON>s", "Threat Indicators", "Suspicious Activity"], "suspiciousActivities": [], "recommendations": [], "threatIndicators": [], "riskFactors": [{"factor": "Transaction Volume", "weight": 0.2, "score": 0, "description": "Total transaction volume: 0.0000 BTC", "evidence": ["0 transactions", "0.0000 BTC total"]}, {"factor": "Address Diversity", "weight": 0.15, "score": 0, "description": "Number of unique addresses involved: 0", "evidence": ["0 unique addresses"]}, {"factor": "Timing Pat<PERSON>s", "weight": 0.2, "score": 2, "description": "Normal timing patterns", "evidence": ["Average interval: 0s", "Rapid succession count: 0"]}, {"factor": "Threat Indicators", "weight": 0.25, "score": 0, "description": "0 threat indicators identified", "evidence": []}, {"factor": "Suspicious Activity", "weight": 0.2, "score": 0, "description": "Overall suspicion level: MINIMAL", "evidence": []}], "complianceFlags": {"amlConcerns": false, "sanctionsRisk": false, "jurisdictionalIssues": false, "reportingRequirements": []}, "investigationPriority": "LOW", "actionableIntelligence": {"immediateActions": ["Freeze related accounts if possible", "Document all evidence", "Notify relevant authorities"], "investigationSteps": ["Trace funds to final destinations", "Identify beneficial owners", "Gather additional transaction data"], "legalConsiderations": ["Preserve evidence for legal proceedings", "Consider regulatory reporting requirements", "Consult with legal counsel"], "evidenceRequirements": ["Blockchain transaction records", "Exchange KYC information", "Timing analysis documentation"]}, "riskTrends": {"increasing": false, "timeframe": "24 hours", "trendFactors": ["No recent activity"]}}}, "integrity": {"totalTransactions": 0, "totalEvidence": 0, "checksum": "mp7xo2"}}