{"metadata": {"investigationId": "53c45fe1-5aed-4f9b-894c-4d51ae5d898f", "exportTimestamp": "2025-07-20T14:12:52.659Z", "toolVersion": "3.0.0", "evidenceCount": 1, "categoryCount": 5}, "categories": [{"id": "transaction-evidence", "name": "Transaction Evidence", "description": "Bitcoin transaction data and related blockchain evidence", "priority": "HIGH", "tags": ["blockchain", "transaction", "financial"]}, {"id": "address-evidence", "name": "Address Evidence", "description": "Bitcoin address information and activity patterns", "priority": "MEDIUM", "tags": ["blockchain", "address", "identity"]}, {"id": "pattern-evidence", "name": "Pattern Evidence", "description": "Suspicious activity patterns and behavioral analysis", "priority": "HIGH", "tags": ["analysis", "pattern", "suspicious"]}, {"id": "risk-evidence", "name": "Risk Assessment Evidence", "description": "Risk scoring and threat assessment data", "priority": "CRITICAL", "tags": ["risk", "assessment", "threat"]}, {"id": "technical-evidence", "name": "Technical Evidence", "description": "Technical analysis and forensic artifacts", "priority": "MEDIUM", "tags": ["technical", "forensic", "analysis"]}], "evidence": [{"evidenceId": "57cd1e8c-146e-451b-9671-9b228dec86aa", "investigationId": "53c45fe1-5aed-4f9b-894c-4d51ae5d898f", "evidenceType": "address", "description": "Bitcoin address **********************************", "data": {"address": "**********************************", "addressType": "legacy", "totalReceived": 0, "totalSent": 0, "balance": 0, "transactionCount": 0, "firstSeen": "2025-07-20T14:12:50.740Z"}, "timestamp": "2025-07-20T14:12:51.516Z", "hashValue": "31c3cae84c5755a6d0d473661a5deb05d223373ce28d8b7d506ac5ba42467c9f", "chainOfCustody": [{"timestamp": "2025-07-20T14:12:51.516Z", "action": "evidence_created", "userId": "system", "description": "Evidence item created: Bitcoin address **********************************"}, {"timestamp": "2025-07-20T14:12:51.517Z", "action": "evidence_collected", "userId": "investigator", "description": "Address evidence collected during investigation"}], "category": {"id": "address-evidence", "name": "Address Evidence", "description": "Bitcoin address information and activity patterns", "priority": "MEDIUM", "tags": ["blockchain", "address", "identity"]}, "integrityChecks": [{"evidenceId": "57cd1e8c-146e-451b-9671-9b228dec86aa", "originalHash": "31c3cae84c5755a6d0d473661a5deb05d223373ce28d8b7d506ac5ba42467c9f", "currentHash": "31c3cae84c5755a6d0d473661a5deb05d223373ce28d8b7d506ac5ba42467c9f", "isValid": true, "timestamp": "2025-07-20T14:12:51.516Z", "verifiedBy": "system"}], "relatedEvidence": [], "metadata": {"source": "blockchain_api", "collectionMethod": "automated_address_analysis", "jurisdiction": "international", "legalBasis": "financial_crime_investigation", "retentionPeriod": "7_years", "accessLevel": "RESTRICTED"}, "attachments": []}], "integrity": {"packageHash": "e1c066c8548290d80c0b76f8f0986aca5218cd35e1d340392bbcc0b41c79079d", "verificationInstructions": "Verify package integrity by recalculating hash of evidence data"}, "legalNotice": {"disclaimer": "This evidence package was generated by automated forensic tools and should be verified by qualified professionals.", "chainOfCustody": "All evidence items include detailed chain of custody documentation.", "retention": "Evidence should be retained according to applicable legal requirements."}}