
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bitcoin Investigation Risk Analysis - 53c45fe1-5aed-4f9b-894c-4d51ae5d898f</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; border-radius: 10px; padding: 30px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
        .chart-container { height: 400px; margin: 30px 0; }
        .risk-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin: 30px 0; }
        .risk-card { padding: 20px; border-radius: 8px; text-align: center; }
        .risk-card.low { background: #d4edda; border: 2px solid #28a745; }
        .risk-card.medium { background: #fff3cd; border: 2px solid #ffc107; }
        .risk-card.high { background: #f8d7da; border: 2px solid #dc3545; }
        .risk-card.critical { background: #f5c6cb; border: 2px solid #dc3545; }
        .risk-value { font-size: 2.5em; font-weight: bold; margin-bottom: 10px; }
        .risk-label { font-size: 1.1em; font-weight: bold; margin-bottom: 5px; }
        .risk-amount { font-size: 0.9em; color: #666; }
        .summary { background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>⚠️ Risk Analysis Dashboard</h1>
        <p>Investigation ID: 53c45fe1-5aed-4f9b-894c-4d51ae5d898f | Generated: 7/20/2025, 4:12:52 PM</p>

        <div class="summary">
            <h2>Risk Assessment Summary</h2>
            <p>This analysis categorizes transactions by risk level based on amount, patterns, and suspicious indicators.</p>
        </div>

        <div class="risk-grid">
            
                <div class="risk-card low">
                    <div class="risk-value">0</div>
                    <div class="risk-label">LOW RISK</div>
                    <div class="risk-amount">0.0000 BTC</div>
                </div>
            
                <div class="risk-card medium">
                    <div class="risk-value">0</div>
                    <div class="risk-label">MEDIUM RISK</div>
                    <div class="risk-amount">0.0000 BTC</div>
                </div>
            
                <div class="risk-card high">
                    <div class="risk-value">0</div>
                    <div class="risk-label">HIGH RISK</div>
                    <div class="risk-amount">0.0000 BTC</div>
                </div>
            
                <div class="risk-card critical">
                    <div class="risk-value">0</div>
                    <div class="risk-label">CRITICAL RISK</div>
                    <div class="risk-amount">0.0000 BTC</div>
                </div>
            
        </div>

        <h2>Risk Distribution Charts</h2>

        <div class="chart-container">
            <canvas id="riskByCount"></canvas>
        </div>

        <div class="chart-container">
            <canvas id="riskByAmount"></canvas>
        </div>

        <div class="summary">
            <h3>Risk Indicators</h3>
            <ul>
                <li><strong>Low Risk:</strong> Small amounts, regular patterns, known addresses</li>
                <li><strong>Medium Risk:</strong> Moderate amounts, some unusual patterns</li>
                <li><strong>High Risk:</strong> Large amounts, suspicious patterns, unknown destinations</li>
                <li><strong>Critical Risk:</strong> Very large amounts, mixing services, high anonymity</li>
            </ul>
        </div>
    </div>

    <script>
        // Risk by count chart
        const ctx1 = document.getElementById('riskByCount').getContext('2d');
        new Chart(ctx1, {
            type: 'doughnut',
            data: {
                labels: ["LOW Risk","MEDIUM Risk","HIGH Risk","CRITICAL Risk"],
                datasets: [{
                    data: [0,0,0,0],
                    backgroundColor: ['#28a745', '#ffc107', '#fd7e14', '#dc3545'],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: { display: true, text: 'Risk Distribution by Transaction Count', font: { size: 16 } },
                    legend: { position: 'bottom' }
                }
            }
        });

        // Risk by amount chart
        const ctx2 = document.getElementById('riskByAmount').getContext('2d');
        new Chart(ctx2, {
            type: 'bar',
            data: {
                labels: ["LOW","MEDIUM","HIGH","CRITICAL"],
                datasets: [{
                    label: 'Bitcoin Amount',
                    data: [0,0,0,0],
                    backgroundColor: ['#28a745', '#ffc107', '#fd7e14', '#dc3545'],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: { display: true, text: 'Risk Distribution by Bitcoin Amount', font: { size: 16 } },
                    legend: { display: false }
                },
                scales: {
                    y: { beginAtZero: true, title: { display: true, text: 'Bitcoin (BTC)' } },
                    x: { title: { display: true, text: 'Risk Level' } }
                }
            }
        });
    </script>
</body>
</html>