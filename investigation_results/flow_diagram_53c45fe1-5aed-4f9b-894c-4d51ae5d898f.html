
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bitcoin Fund Flow Diagram - 53c45fe1-5aed-4f9b-894c-4d51ae5d898f</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1400px; margin: 0 auto; background: white; border-radius: 10px; padding: 30px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
        .chart-container { height: 500px; margin: 30px 0; }
        .flow-summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 30px 0; }
        .flow-card { background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #667eea; }
        .flow-value { font-size: 2em; font-weight: bold; color: #667eea; }
        .flow-label { color: #666; font-size: 0.9em; }
        .destinations { background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .destination-item { display: inline-block; background: white; padding: 10px; margin: 5px; border-radius: 5px; border: 1px solid #ddd; font-family: monospace; font-size: 0.9em; }
        .alert { padding: 15px; margin: 20px 0; border-radius: 8px; }
        .alert-warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        .alert-info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
    </style>
</head>
<body>
    <div class="container">
        <h1>💰 Bitcoin Fund Flow Analysis</h1>
        <p>Investigation ID: 53c45fe1-5aed-4f9b-894c-4d51ae5d898f | Generated: 7/20/2025, 4:12:52 PM</p>

        <div class="flow-summary">
            <div class="flow-card">
                <div class="flow-value">0.0000</div>
                <div class="flow-label">Total Bitcoin Traced</div>
            </div>
            <div class="flow-card">
                <div class="flow-value">0</div>
                <div class="flow-label">Transaction Hops</div>
            </div>
            <div class="flow-card">
                <div class="flow-value">1</div>
                <div class="flow-label">Unique Addresses</div>
            </div>
            <div class="flow-card">
                <div class="flow-value">0</div>
                <div class="flow-label">Suspicious Activities</div>
            </div>
            <div class="flow-card">
                <div class="flow-value">0</div>
                <div class="flow-label">Exchange Deposits</div>
            </div>
            <div class="flow-card">
                <div class="flow-value">0</div>
                <div class="flow-label">Mixing Services</div>
            </div>
        </div>

        

        <h2>Fund Flow Over Time</h2>
        <div class="chart-container">
            <canvas id="flowChart"></canvas>
        </div>

        <h2>Transaction Volume Distribution</h2>
        <div class="chart-container">
            <canvas id="volumeChart"></canvas>
        </div>

        <div class="destinations">
            <h3>Final Destinations (0 addresses)</h3>
            <p>These are the addresses where funds were last observed:</p>
            
        </div>

        <div class="alert alert-info">
            <strong>💡 Understanding Fund Flow:</strong>
            <ul>
                <li><strong>Transaction Hops:</strong> Number of times funds moved between addresses</li>
                <li><strong>Suspicious Activities:</strong> Transactions flagged for unusual patterns</li>
                <li><strong>Exchange Deposits:</strong> Funds sent to known cryptocurrency exchanges</li>
                <li><strong>Mixing Services:</strong> Use of privacy-enhancing services to obscure fund trails</li>
                <li><strong>Final Destinations:</strong> Last known addresses in the investigation trail</li>
            </ul>
        </div>
    </div>

    <script>
        // Fund flow over time chart
        const ctx1 = document.getElementById('flowChart').getContext('2d');
        new Chart(ctx1, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: 'Bitcoin Amount',
                    data: [],
                    borderColor: '#667eea',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: { display: true, text: 'Bitcoin Fund Flow Timeline', font: { size: 16 } },
                    legend: { display: false }
                },
                scales: {
                    y: { beginAtZero: true, title: { display: true, text: 'Bitcoin (BTC)' } },
                    x: { title: { display: true, text: 'Date' } }
                }
            }
        });

        // Transaction volume distribution
        const ctx2 = document.getElementById('volumeChart').getContext('2d');
        const volumeRanges = ['< 0.1 BTC', '0.1 - 1 BTC', '1 - 10 BTC', '> 10 BTC'];
        const volumeData = [
            0,
            0,
            0,
            0
        ];

        new Chart(ctx2, {
            type: 'bar',
            data: {
                labels: volumeRanges,
                datasets: [{
                    label: 'Number of Transactions',
                    data: volumeData,
                    backgroundColor: ['#28a745', '#ffc107', '#fd7e14', '#dc3545'],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: { display: true, text: 'Transaction Volume Distribution', font: { size: 16 } },
                    legend: { display: false }
                },
                scales: {
                    y: { beginAtZero: true, title: { display: true, text: 'Number of Transactions' } },
                    x: { title: { display: true, text: 'Transaction Size' } }
                }
            }
        });
    </script>
</body>
</html>