
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive Bitcoin Investigation Report - 53c45fe1-5aed-4f9b-894c-4d51ae5d898f</title>
    <script src="https://cdn.jsdelivr.net/npm/vis-network@latest/dist/vis-network.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1400px; margin: 0 auto; background: white; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 10px 10px 0 0; }
        .header h1 { margin: 0; font-size: 2.5em; }
        .header p { margin: 10px 0 0 0; opacity: 0.9; }
        .nav-tabs { display: flex; background: #f8f9fa; border-bottom: 1px solid #dee2e6; }
        .nav-tab { padding: 15px 25px; cursor: pointer; border: none; background: none; font-size: 16px; transition: all 0.3s; }
        .nav-tab.active { background: white; border-bottom: 3px solid #667eea; color: #667eea; font-weight: bold; }
        .nav-tab:hover { background: #e9ecef; }
        .tab-content { display: none; padding: 30px; }
        .tab-content.active { display: block; }
        .summary-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .summary-card { background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #667eea; }
        .summary-card h3 { margin: 0 0 10px 0; color: #333; }
        .summary-card .value { font-size: 2em; font-weight: bold; color: #667eea; }
        .summary-card .label { color: #666; font-size: 0.9em; }
        .visualization-container { height: 600px; border: 1px solid #dee2e6; border-radius: 8px; margin: 20px 0; }
        .risk-indicator { display: inline-block; padding: 4px 8px; border-radius: 4px; font-size: 0.8em; font-weight: bold; }
        .risk-low { background: #d4edda; color: #155724; }
        .risk-medium { background: #fff3cd; color: #856404; }
        .risk-high { background: #f8d7da; color: #721c24; }
        .risk-critical { background: #f5c6cb; color: #721c24; }
        .timeline-item { display: flex; align-items: center; padding: 15px; border-bottom: 1px solid #eee; }
        .timeline-time { min-width: 150px; font-weight: bold; color: #667eea; }
        .timeline-content { flex: 1; }
        .timeline-amount { font-weight: bold; color: #28a745; }
        .alert { padding: 15px; margin: 20px 0; border-radius: 8px; }
        .alert-warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        .alert-danger { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .alert-info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 Interactive Bitcoin Investigation Report</h1>
            <p>Investigation ID: 53c45fe1-5aed-4f9b-894c-4d51ae5d898f | Generated: 7/20/2025, 4:12:52 PM</p>
        </div>

        <div class="nav-tabs">
            <button class="nav-tab active" onclick="showTab('overview')">Overview</button>
            <button class="nav-tab" onclick="showTab('network')">Network Graph</button>
            <button class="nav-tab" onclick="showTab('timeline')">Timeline</button>
            <button class="nav-tab" onclick="showTab('risk')">Risk Analysis</button>
            <button class="nav-tab" onclick="showTab('flow')">Fund Flow</button>
        </div>

        <div id="overview" class="tab-content active">
            <h2>Investigation Overview</h2>
            <div class="summary-grid">
                <div class="summary-card">
                    <h3>Total Amount</h3>
                    <div class="value">0.0000</div>
                    <div class="label">Bitcoin (BTC)</div>
                </div>
                <div class="summary-card">
                    <h3>Transactions</h3>
                    <div class="value">0</div>
                    <div class="label">Total traced</div>
                </div>
                <div class="summary-card">
                    <h3>Addresses</h3>
                    <div class="value">1</div>
                    <div class="label">Unique addresses</div>
                </div>
                <div class="summary-card">
                    <h3>Risk Level</h3>
                    <div class="value">MINIMAL</div>
                    <div class="label">Overall assessment</div>
                </div>
            </div>

            

            <h3>Investigation Summary</h3>
            <p><strong>Initial Transaction:</strong> f4184fc596403b9d638783cf57adfe4c75c605f6356fbc91338530e9831e9e16</p>
            <p><strong>Target Address:</strong> **********************************</p>
            <p><strong>Investigation Depth:</strong> 5 levels</p>
            <p><strong>Risk Score:</strong> 0.40/10</p>

            <h3>Key Findings</h3>
            <ul>
                <li>Traced 0.0000 BTC through 0 transactions</li>
                <li>Funds passed through 1 unique addresses</li>
                <li>0 transactions flagged as suspicious</li>
                <li>Final destinations: 0 addresses</li>
            </ul>
        </div>

        <div id="network" class="tab-content">
            <h2>Transaction Network Graph</h2>
            <p>Interactive visualization of Bitcoin address relationships and transaction flows.</p>
            <div id="networkGraph" class="visualization-container"></div>
            <div class="alert alert-info">
                <strong>💡 How to use:</strong> Click and drag nodes to explore the network.
                Hover over nodes and edges for detailed information.
                Use mouse wheel to zoom in/out.
            </div>
        </div>

        <div id="timeline" class="tab-content">
            <h2>Transaction Timeline</h2>
            <p>Chronological view of all transactions in the investigation.</p>
            <div class="timeline">
                
            </div>
        </div>

        <div id="risk" class="tab-content">
            <h2>Risk Analysis</h2>
            <p>Comprehensive risk assessment of the investigation.</p>
            <div id="riskChart" class="visualization-container"></div>
            <div class="summary-grid">
                
                    <div class="summary-card">
                        <h3>LOW Risk</h3>
                        <div class="value">0</div>
                        <div class="label">0.0000 BTC</div>
                    </div>
                
                    <div class="summary-card">
                        <h3>MEDIUM Risk</h3>
                        <div class="value">0</div>
                        <div class="label">0.0000 BTC</div>
                    </div>
                
                    <div class="summary-card">
                        <h3>HIGH Risk</h3>
                        <div class="value">0</div>
                        <div class="label">0.0000 BTC</div>
                    </div>
                
                    <div class="summary-card">
                        <h3>CRITICAL Risk</h3>
                        <div class="value">0</div>
                        <div class="label">0.0000 BTC</div>
                    </div>
                
            </div>
        </div>

        <div id="flow" class="tab-content">
            <h2>Fund Flow Analysis</h2>
            <p>Detailed analysis of how funds moved through the Bitcoin network.</p>
            <div id="flowChart" class="visualization-container"></div>
            <h3>Flow Summary</h3>
            <ul>
                <li><strong>Starting Amount:</strong> 0.0000 BTC</li>
                <li><strong>Transaction Hops:</strong> 0</li>
                <li><strong>Address Changes:</strong> 1</li>
                <li><strong>Suspicious Activities:</strong> 0</li>
                <li><strong>Final Destinations:</strong> 0 addresses</li>
            </ul>
        </div>
    </div>

    <script>
        // Tab switching functionality
        function showTab(tabName) {
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });

            // Remove active class from all tabs
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // Show selected tab
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');

            // Initialize visualizations when tabs are shown
            if (tabName === 'network') {
                initNetworkGraph();
            } else if (tabName === 'risk') {
                initRiskChart();
            } else if (tabName === 'flow') {
                initFlowChart();
            }
        }

        // Network graph initialization
        function initNetworkGraph() {
            const container = document.getElementById('networkGraph');
            const nodes = new vis.DataSet([{"id":"**********************************","label":"1A1zP1eP...v7DivfNa","type":"address","value":0,"level":0,"metadata":{"address":"**********************************","suspicious":false}}]);
            const edges = new vis.DataSet([]);

            const graphData = { nodes: nodes, edges: edges };
            const options = {
                nodes: {
                    shape: 'dot',
                    size: 16,
                    font: { size: 12 },
                    borderWidth: 2,
                    shadow: true
                },
                edges: {
                    width: 2,
                    shadow: true,
                    arrows: { to: { enabled: true, scaleFactor: 1 } }
                },
                physics: {
                    enabled: true,
                    stabilization: { iterations: 100 }
                },
                interaction: {
                    hover: true,
                    tooltipDelay: 200
                }
            };

            new vis.Network(container, graphData, options);
        }

        // Risk chart initialization
        function initRiskChart() {
            const ctx = document.getElementById('riskChart').getContext('2d');
            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ["LOW","MEDIUM","HIGH","CRITICAL"],
                    datasets: [{
                        data: [0,0,0,0],
                        backgroundColor: ['#28a745', '#ffc107', '#fd7e14', '#dc3545']
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { position: 'bottom' },
                        title: { display: true, text: 'Risk Distribution by Transaction Count' }
                    }
                }
            });
        }

        // Flow chart initialization
        function initFlowChart() {
            const ctx = document.getElementById('flowChart').getContext('2d');
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'Bitcoin Amount',
                        data: [],
                        borderColor: '#667eea',
                        backgroundColor: 'rgba(102, 126, 234, 0.1)',
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: { display: true, text: 'Fund Flow Over Time' }
                    },
                    scales: {
                        y: { beginAtZero: true, title: { display: true, text: 'Bitcoin (BTC)' } },
                        x: { title: { display: true, text: 'Date' } }
                    }
                }
            });
        }

        // Initialize network graph on page load
        document.addEventListener('DOMContentLoaded', function() {
            initNetworkGraph();
        });
    </script>
</body>
</html>