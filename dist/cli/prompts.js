"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CLIPrompts = void 0;
const inquirer_1 = __importDefault(require("inquirer"));
const chalk_1 = __importDefault(require("chalk"));
const validation_1 = require("../utils/validation");
class CLIPrompts {
    async getVictimInformation() {
        console.log(chalk_1.default.blue('\n📋 VICTIM INFORMATION'));
        console.log(chalk_1.default.gray('Please provide the following information about the scam incident:\n'));
        const answers = await inquirer_1.default.prompt([
            {
                type: 'input',
                name: 'victimName',
                message: 'Your full name:',
                validate: (input) => {
                    if (!input || input.trim().length === 0) {
                        return 'Name is required';
                    }
                    if (input.trim().length < 2) {
                        return 'Name must be at least 2 characters';
                    }
                    return true;
                },
                filter: (input) => input.trim(),
            },
            {
                type: 'input',
                name: 'contactInfo',
                message: 'Your email address (optional):',
                validate: (input) => {
                    if (!input || input.trim().length === 0) {
                        return true; // Optional field
                    }
                    return (0, validation_1.validateEmail)(input) || 'Please enter a valid email address';
                },
                filter: (input) => input.trim(),
            },
            {
                type: 'input',
                name: 'incidentDate',
                message: 'Date of the scam incident (YYYY-MM-DD, optional):',
                validate: (input) => {
                    if (!input || input.trim().length === 0) {
                        return true; // Optional field
                    }
                    return (0, validation_1.validateDate)(input) || 'Please enter a valid date in YYYY-MM-DD format';
                },
                filter: (input) => input.trim(),
            },
            {
                type: 'editor',
                name: 'caseDescription',
                message: 'Describe what happened (this will help with the investigation):',
                validate: (input) => {
                    if (!input || input.trim().length === 0) {
                        return 'Case description is required';
                    }
                    if (input.trim().length < 10) {
                        return 'Please provide a more detailed description (at least 10 characters)';
                    }
                    return true;
                },
                filter: (input) => input.trim(),
            },
        ]);
        return answers;
    }
    async getTransactionDetails() {
        console.log(chalk_1.default.blue('\n🔍 TRANSACTION DETAILS'));
        console.log(chalk_1.default.gray('Please provide the Bitcoin transaction information:\n'));
        const answers = await inquirer_1.default.prompt([
            {
                type: 'input',
                name: 'initialTxid',
                message: 'Transaction ID (TXID) where you sent Bitcoin to the scammer:',
                validate: (input) => {
                    if (!input || input.trim().length === 0) {
                        return 'Transaction ID is required';
                    }
                    return ((0, validation_1.validateTransactionId)(input.trim()) ||
                        'Please enter a valid 64-character transaction ID');
                },
                filter: (input) => input.trim(),
            },
            {
                type: 'input',
                name: 'targetAddress',
                message: "Bitcoin address where you sent the funds (scammer's address):",
                validate: (input) => {
                    if (!input || input.trim().length === 0) {
                        return 'Bitcoin address is required';
                    }
                    return (0, validation_1.validateBitcoinAddress)(input.trim()) || 'Please enter a valid Bitcoin address';
                },
                filter: (input) => input.trim(),
            },
            {
                type: 'number',
                name: 'scamAmount',
                message: 'Amount of Bitcoin you lost (in BTC):',
                validate: (input) => {
                    if (!input || isNaN(input)) {
                        return 'Please enter a valid amount';
                    }
                    return ((0, validation_1.validateBtcAmount)(input) ||
                        'Please enter a valid Bitcoin amount (0 < amount <= 21,000,000)');
                },
            },
        ]);
        return answers;
    }
    async getInvestigationSettings() {
        console.log(chalk_1.default.blue('\n⚙️  INVESTIGATION SETTINGS'));
        console.log(chalk_1.default.gray('Configure how deep you want to trace the transactions:\n'));
        const answers = await inquirer_1.default.prompt([
            {
                type: 'list',
                name: 'maxDepth',
                message: 'Investigation depth (how many transaction hops to follow):',
                choices: [
                    {
                        name: '🚀 Quick (3 hops) - Fast results, basic tracing',
                        value: 3,
                    },
                    {
                        name: '🔍 Standard (5 hops) - Balanced speed and thoroughness',
                        value: 5,
                    },
                    {
                        name: '🕵️  Deep (7 hops) - Comprehensive investigation',
                        value: 7,
                    },
                    {
                        name: '🔬 Maximum (10 hops) - Most thorough, may take longer',
                        value: 10,
                    },
                ],
                default: 5,
            },
            {
                type: 'confirm',
                name: 'enableAdvancedAnalysis',
                message: 'Enable advanced pattern analysis and AI insights?',
                default: true,
            },
            {
                type: 'confirm',
                name: 'generateReports',
                message: 'Generate comprehensive reports and evidence package?',
                default: true,
            },
        ]);
        return answers;
    }
    async confirmInvestigation(userInput) {
        console.log(chalk_1.default.yellow('\n📋 INVESTIGATION SUMMARY'));
        console.log(chalk_1.default.gray('Please review the information before starting:\n'));
        console.log(chalk_1.default.white('Victim Information:'));
        console.log(`  Name: ${chalk_1.default.cyan(userInput.victimName)}`);
        if (userInput.contactInfo) {
            console.log(`  Email: ${chalk_1.default.cyan(userInput.contactInfo)}`);
        }
        if (userInput.incidentDate) {
            console.log(`  Incident Date: ${chalk_1.default.cyan(userInput.incidentDate)}`);
        }
        console.log(chalk_1.default.white('\nTransaction Details:'));
        console.log(`  Transaction ID: ${chalk_1.default.cyan(userInput.initialTxid)}`);
        console.log(`  Scammer Address: ${chalk_1.default.cyan(userInput.targetAddress)}`);
        console.log(`  Amount Lost: ${chalk_1.default.red(userInput.scamAmount.toFixed(8))} BTC`);
        console.log(chalk_1.default.white('\nInvestigation Settings:'));
        console.log(`  Max Depth: ${chalk_1.default.cyan(userInput.maxDepth)} hops`);
        console.log(chalk_1.default.white('\nCase Description:'));
        console.log(chalk_1.default.gray(`  ${userInput.caseDescription.substring(0, 200)}${userInput.caseDescription.length > 200 ? '...' : ''}`));
        const { confirmed } = await inquirer_1.default.prompt([
            {
                type: 'confirm',
                name: 'confirmed',
                message: '\nStart the investigation with this information?',
                default: true,
            },
        ]);
        return confirmed;
    }
    async askForHelp() {
        const { helpTopic } = await inquirer_1.default.prompt([
            {
                type: 'list',
                name: 'helpTopic',
                message: 'What do you need help with?',
                choices: [
                    {
                        name: '🔍 How to find my transaction ID (TXID)',
                        value: 'txid',
                    },
                    {
                        name: '📍 How to find the Bitcoin address I sent to',
                        value: 'address',
                    },
                    {
                        name: '💰 How to determine the amount I lost',
                        value: 'amount',
                    },
                    {
                        name: '⚖️  Legal advice and next steps',
                        value: 'legal',
                    },
                    {
                        name: '🛡️  How to protect myself from future scams',
                        value: 'protection',
                    },
                    {
                        name: '📞 How to report the scam to authorities',
                        value: 'reporting',
                    },
                    {
                        name: '🔙 Go back to main menu',
                        value: 'back',
                    },
                ],
            },
        ]);
        return helpTopic;
    }
    async askToContinue(message = 'Continue?') {
        const { shouldContinue } = await inquirer_1.default.prompt([
            {
                type: 'confirm',
                name: 'shouldContinue',
                message,
                default: true,
            },
        ]);
        return shouldContinue;
    }
    async selectOutputFormat() {
        const { formats } = await inquirer_1.default.prompt([
            {
                type: 'checkbox',
                name: 'formats',
                message: 'Select output formats for your investigation report:',
                choices: [
                    {
                        name: '📄 PDF Report (recommended for legal proceedings)',
                        value: 'pdf',
                        checked: true,
                    },
                    {
                        name: '📊 HTML Report with interactive charts',
                        value: 'html',
                        checked: true,
                    },
                    {
                        name: '📋 Text Report (simple format)',
                        value: 'txt',
                        checked: false,
                    },
                    {
                        name: '💾 JSON Data (for technical analysis)',
                        value: 'json',
                        checked: false,
                    },
                    {
                        name: '📈 CSV Data (for spreadsheet analysis)',
                        value: 'csv',
                        checked: false,
                    },
                ],
                validate: (choices) => {
                    if (choices.length === 0) {
                        return 'Please select at least one output format';
                    }
                    return true;
                },
            },
        ]);
        return formats;
    }
    displayWelcome() {
        console.clear();
        console.log(chalk_1.default.blue.bold(`
╔══════════════════════════════════════════════════════════════════════════════╗
║                    🔍 Bitcoin Forensic Investigation Tool v3.0                ║
║                          For Bitcoin Scam Victims                            ║
╚══════════════════════════════════════════════════════════════════════════════╝
`));
        console.log(chalk_1.default.white('🎯 PURPOSE:'));
        console.log(chalk_1.default.gray('   This tool helps Bitcoin scam victims trace their stolen funds and'));
        console.log(chalk_1.default.gray('   generate professional evidence packages for law enforcement.\n'));
        console.log(chalk_1.default.white('✨ FEATURES:'));
        console.log(chalk_1.default.gray('   • Automated Bitcoin transaction tracing'));
        console.log(chalk_1.default.gray('   • AI-powered suspicious activity detection'));
        console.log(chalk_1.default.gray('   • Professional evidence collection'));
        console.log(chalk_1.default.gray('   • Comprehensive reports for legal proceedings'));
        console.log(chalk_1.default.gray('   • User-friendly interface for non-technical users\n'));
        console.log(chalk_1.default.yellow('⚠️  IMPORTANT LEGAL NOTICE:'));
        console.log(chalk_1.default.gray('   This tool is for legitimate investigation purposes only.'));
        console.log(chalk_1.default.gray('   Always consult with law enforcement and legal professionals.'));
        console.log(chalk_1.default.gray('   Keep all generated reports as evidence for your case.\n'));
        console.log(chalk_1.default.green('🚀 Ready to start your investigation!\n'));
    }
    displayHelp(topic) {
        console.log(chalk_1.default.blue(`\n📚 HELP: ${topic.toUpperCase()}\n`));
        switch (topic) {
            case 'txid':
                console.log(chalk_1.default.white('How to find your Transaction ID (TXID):'));
                console.log(chalk_1.default.gray("1. Check your Bitcoin wallet's transaction history"));
                console.log(chalk_1.default.gray('2. Look for the transaction where you sent Bitcoin to the scammer'));
                console.log(chalk_1.default.gray('3. The TXID is a 64-character string of letters and numbers'));
                console.log(chalk_1.default.gray('4. You can also search on blockchain explorers like:'));
                console.log(chalk_1.default.gray('   • https://mempool.space'));
                console.log(chalk_1.default.gray('   • https://blockstream.info'));
                console.log(chalk_1.default.gray('   • https://blockchain.info'));
                break;
            case 'address':
                console.log(chalk_1.default.white('How to find the Bitcoin address you sent to:'));
                console.log(chalk_1.default.gray("1. Check your wallet's transaction details"));
                console.log(chalk_1.default.gray('2. Look for the "To" or "Recipient" address'));
                console.log(chalk_1.default.gray('3. Bitcoin addresses start with:'));
                console.log(chalk_1.default.gray('   • 1 or 3 (Legacy addresses)'));
                console.log(chalk_1.default.gray('   • bc1 (SegWit addresses)'));
                console.log(chalk_1.default.gray('4. Copy the full address exactly as shown'));
                break;
            case 'amount':
                console.log(chalk_1.default.white('How to determine the amount you lost:'));
                console.log(chalk_1.default.gray("1. Check your wallet's transaction history"));
                console.log(chalk_1.default.gray('2. Look at the amount sent in the transaction'));
                console.log(chalk_1.default.gray('3. Enter the amount in BTC (not USD)'));
                console.log(chalk_1.default.gray('4. Example: 0.05 BTC, not $2,000'));
                break;
            case 'legal':
                console.log(chalk_1.default.white('Legal advice and next steps:'));
                console.log(chalk_1.default.gray('1. File a report with your local police'));
                console.log(chalk_1.default.gray("2. Report to the FBI's IC3 (if in the US)"));
                console.log(chalk_1.default.gray("3. Contact your country's cybercrime unit"));
                console.log(chalk_1.default.gray('4. Keep all evidence and documentation'));
                console.log(chalk_1.default.gray('5. Consider consulting with a lawyer'));
                console.log(chalk_1.default.yellow('⚠️  This tool does not provide legal advice'));
                break;
            case 'protection':
                console.log(chalk_1.default.white('How to protect yourself from future scams:'));
                console.log(chalk_1.default.gray('1. Never send Bitcoin to unknown parties'));
                console.log(chalk_1.default.gray('2. Verify all investment opportunities independently'));
                console.log(chalk_1.default.gray('3. Be suspicious of guaranteed returns'));
                console.log(chalk_1.default.gray('4. Use reputable exchanges and wallets'));
                console.log(chalk_1.default.gray('5. Enable two-factor authentication'));
                console.log(chalk_1.default.gray('6. Keep your private keys secure'));
                break;
            case 'reporting':
                console.log(chalk_1.default.white('How to report the scam to authorities:'));
                console.log(chalk_1.default.gray('🇺🇸 United States:'));
                console.log(chalk_1.default.gray('   • FBI IC3: https://ic3.gov'));
                console.log(chalk_1.default.gray('   • FTC: https://reportfraud.ftc.gov'));
                console.log(chalk_1.default.gray('🇬🇧 United Kingdom:'));
                console.log(chalk_1.default.gray('   • Action Fraud: https://actionfraud.police.uk'));
                console.log(chalk_1.default.gray('🇨🇦 Canada:'));
                console.log(chalk_1.default.gray('   • CAFC: https://antifraudcentre-centreantifraude.ca'));
                console.log(chalk_1.default.gray('🌍 Other countries:'));
                console.log(chalk_1.default.gray('   • Contact your local police cybercrime unit'));
                break;
        }
        console.log(chalk_1.default.gray('\nPress Enter to continue...'));
    }
    displayError(error) {
        console.log(chalk_1.default.red(`\n❌ Error: ${error}\n`));
    }
    displaySuccess(message) {
        console.log(chalk_1.default.green(`\n✅ ${message}\n`));
    }
    displayWarning(message) {
        console.log(chalk_1.default.yellow(`\n⚠️  ${message}\n`));
    }
    displayInfo(message) {
        console.log(chalk_1.default.blue(`\nℹ️  ${message}\n`));
    }
}
exports.CLIPrompts = CLIPrompts;
//# sourceMappingURL=prompts.js.map