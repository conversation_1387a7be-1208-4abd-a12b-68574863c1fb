"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const investigator_1 = require("../core/investigator");
const unified_reporting_1 = require("../services/unified-reporting");
const visualization_1 = require("../services/visualization");
const fs_1 = __importDefault(require("fs"));
// Mock external dependencies
jest.mock('fs');
jest.mock('../services/api');
const mockFs = fs_1.default;
describe('Integration Tests', () => {
    let investigator;
    let reportingService;
    let visualizationService;
    const testOutputDir = './test-integration-output';
    beforeEach(() => {
        jest.clearAllMocks();
        // Mock fs operations
        mockFs.existsSync.mockReturnValue(false);
        mockFs.mkdirSync.mockImplementation(() => undefined);
        mockFs.writeFileSync.mockImplementation(() => undefined);
        mockFs.readFileSync.mockReturnValue('test file content');
        // Initialize services
        investigator = new investigator_1.BitcoinForensicsInvestigator({
            outputDirectory: testOutputDir,
            maxDepth: 3,
            verboseLogging: false,
        });
        reportingService = new unified_reporting_1.UnifiedReportingService(testOutputDir);
        visualizationService = new visualization_1.VisualizationService({
            outputDirectory: testOutputDir,
        });
    });
    describe('End-to-End Investigation Flow', () => {
        const validUserInput = {
            initialTxid: 'a1b2c3d4e5f67890123456789012345678901234567890123456789012345678',
            targetAddress: '**********************************',
            maxDepth: 3,
            victimName: 'Test Victim',
            caseDescription: 'Integration test case',
            scamAmount: 0.5,
        };
        it('should complete full investigation workflow', async () => {
            // Mock API responses
            const mockApiService = require('../services/api').BitcoinAPIService;
            mockApiService.prototype.getTransaction = jest.fn().mockResolvedValue({
                txid: validUserInput.initialTxid,
                inputs: [{ address: 'input-address', value: 50000000 }],
                outputs: [{ address: validUserInput.targetAddress, value: 50000000 }],
                blockHeight: 800000,
                timestamp: '2024-01-15T10:30:00.000Z',
                confirmations: 6,
                fees: 1000,
            });
            mockApiService.prototype.getAddressInfo = jest.fn().mockResolvedValue({
                address: validUserInput.targetAddress,
                totalReceived: 0.5,
                totalSent: 0.3,
                transactionCount: 5,
                balance: 0.2,
            });
            mockApiService.prototype.getAddressTransactions = jest.fn().mockResolvedValue([
                {
                    txid: 'tx1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef',
                    inputs: [{ address: validUserInput.targetAddress, value: 30000000 }],
                    outputs: [{ address: 'output-address', value: 30000000 }],
                    blockHeight: 800001,
                    timestamp: '2024-01-15T11:30:00.000Z',
                    confirmations: 5,
                    fees: 1000,
                },
            ]);
            // Run investigation
            const results = await investigator.startInvestigation(validUserInput);
            // Verify investigation completed
            expect(results).toBeDefined();
            expect(results.investigationId).toBeDefined();
            expect(results.inputParameters).toEqual(validUserInput);
            expect(results.basicResults).toBeDefined();
            expect(results.detailedTransactions).toBeDefined();
            expect(results.addressAnalysis).toBeDefined();
        });
        it('should handle investigation errors gracefully', async () => {
            const mockApiService = require('../services/api').BitcoinAPIService;
            mockApiService.prototype.getTransaction = jest.fn().mockRejectedValue(new Error('API Error'));
            await expect(investigator.startInvestigation(validUserInput)).rejects.toThrow();
        });
    });
    describe('Reporting Integration', () => {
        const mockResults = {
            investigationId: 'test-integration-123',
            timestamp: '2024-01-15T10:30:00.000Z',
            inputParameters: {
                initialTxid: 'a1b2c3d4e5f67890123456789012345678901234567890123456789012345678',
                targetAddress: '**********************************',
                maxDepth: 3,
                victimName: 'Test Victim',
                caseDescription: 'Integration test',
            },
            basicResults: {
                transactionCount: 5,
                totalAmount: 1.0,
                addressCount: 4,
            },
            detailedTransactions: [
                {
                    txid: 'tx1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef',
                    fromAddress: '**********************************',
                    toAddress: '**********************************',
                    amountBtc: 0.5,
                    depth: 1,
                    timestamp: '2024-01-15T10:30:00.000Z',
                    blockHeight: 800000,
                    confirmations: true,
                    investigationId: 'test-investigation-id',
                    fees: 0.0001,
                },
            ],
            addressAnalysis: [
                {
                    address: '**********************************',
                    addressType: 'legacy',
                    totalReceived: 0.5,
                    totalSent: 0.5,
                    balance: 0.0,
                    transactionCount: 1,
                    firstSeen: '2024-01-15T10:30:00.000Z',
                    lastSeen: '2024-01-15T10:30:00.000Z',
                    riskScore: 3,
                },
            ],
            advancedAnalysis: {
                riskAssessment: {
                    finalRiskLevel: 'LOW',
                    compositeRiskScore: 3.0,
                    baseRiskScore: 2.0,
                    suspicionScore: 2.0,
                    riskFactors: ['low_transaction_volume'],
                    suspiciousActivities: [],
                    recommendations: ['No immediate action required'],
                },
                suspiciousActivity: {
                    overallSuspicionScore: 2.0,
                    suspicionLevel: 'LOW',
                    mixingServices: { detected: false, severity: 0, confidence: 0, details: {} },
                    exchangeDeposits: { detected: false, severity: 0, confidence: 0, details: {} },
                    peelChains: { detected: false, severity: 0, confidence: 0, details: {} },
                    consolidationPatterns: { detected: false, severity: 0, confidence: 0, details: {} },
                    privacyCoinInteractions: { detected: false, severity: 0, confidence: 0, details: {} },
                },
                transactionPatterns: {
                    timingAnalysis: {
                        averageIntervalSeconds: 0,
                        rapidSuccessionCount: 0,
                        totalIntervals: 0,
                        suspiciousTiming: false,
                        timePatterns: [],
                    },
                    amountAnalysis: {
                        totalAmount: 0.5,
                        averageAmount: 0.5,
                        maxAmount: 0.5,
                        minAmount: 0.5,
                        roundAmountsCount: 0,
                        similarAmountsCount: 0,
                        potentialStructuring: false,
                        potentialSplitting: false,
                    },
                    addressReuse: {
                        uniqueFromAddresses: 1,
                        uniqueToAddresses: 1,
                        reusedFromAddresses: {},
                        reusedToAddresses: {},
                        addressReuseDetected: false,
                    },
                    clusteringHints: {
                        totalClusters: 0,
                        potentialClusters: 0,
                        clusterDetails: {},
                        clusteringDetected: false,
                    },
                    riskIndicators: {
                        riskScore: 3.0,
                        riskLevel: 'LOW',
                        riskFactors: ['low_transaction_volume'],
                        totalAmount: 0.5,
                        transactionCount: 1,
                        maxDepth: 1,
                    },
                },
            },
            investigationSummary: {
                status: 'completed',
                keyMetrics: {
                    totalTransactions: 1,
                    totalAmountBtc: 0.5,
                    uniqueAddresses: 1,
                    maximumDepth: 1,
                    riskLevel: 'LOW',
                },
                keyConcerns: ['Low risk investigation'],
                investigationQuality: {
                    qualityScore: 90,
                    qualityLevel: 'HIGH',
                    qualityFactors: ['complete_transaction_trace'],
                    completenessPercentage: 90,
                },
                nextSteps: ['No further action needed'],
                timeline: [],
            },
            evidencePackage: [],
            auditTrail: [],
        };
        it('should generate comprehensive reports', async () => {
            const reportResults = await reportingService.generateReports(mockResults, undefined, undefined, {
                includeVictimFriendly: true,
                includeTechnical: true,
                includeVisualizations: true,
                outputFormats: ['html', 'text', 'json'],
            });
            expect(reportResults).toBeDefined();
            expect(reportResults.allFiles).toBeDefined();
            expect(reportResults.allFiles.length).toBeGreaterThan(0);
            // Verify files were written
            expect(mockFs.writeFileSync).toHaveBeenCalled();
        });
        it('should generate visualizations', async () => {
            const visualizationResults = await visualizationService.generateVisualizations(mockResults, 'test-integration-123');
            expect(visualizationResults).toBeDefined();
            expect(visualizationResults.interactiveReport).toBeDefined();
            expect(visualizationResults.networkGraph).toBeDefined();
            expect(visualizationResults.timeline).toBeDefined();
            expect(visualizationResults.riskChart).toBeDefined();
            expect(visualizationResults.flowDiagram).toBeDefined();
        });
    });
    describe('Error Handling Integration', () => {
        it('should handle file system errors', async () => {
            mockFs.writeFileSync.mockImplementation(() => {
                throw new Error('Disk full');
            });
            const mockResults = {
                investigationId: 'error-test',
                timestamp: '2024-01-15T10:30:00.000Z',
                inputParameters: {
                    initialTxid: 'a1b2c3d4e5f67890123456789012345678901234567890123456789012345678',
                    targetAddress: '**********************************',
                    maxDepth: 1,
                    victimName: 'Test',
                    caseDescription: 'Test',
                },
                basicResults: {
                    transactionCount: 0,
                    totalAmount: 0,
                    addressCount: 0,
                },
                detailedTransactions: [],
                addressAnalysis: [],
                advancedAnalysis: {
                    riskAssessment: {
                        finalRiskLevel: 'LOW',
                        compositeRiskScore: 0,
                        baseRiskScore: 0,
                        suspicionScore: 0,
                        riskFactors: [],
                        suspiciousActivities: [],
                        recommendations: [],
                    },
                    suspiciousActivity: {
                        overallSuspicionScore: 0,
                        suspicionLevel: 'LOW',
                        mixingServices: { detected: false, severity: 0, confidence: 0, details: {} },
                        exchangeDeposits: { detected: false, severity: 0, confidence: 0, details: {} },
                        peelChains: { detected: false, severity: 0, confidence: 0, details: {} },
                        consolidationPatterns: { detected: false, severity: 0, confidence: 0, details: {} },
                        privacyCoinInteractions: { detected: false, severity: 0, confidence: 0, details: {} },
                    },
                    transactionPatterns: {
                        timingAnalysis: {
                            averageIntervalSeconds: 0,
                            rapidSuccessionCount: 0,
                            totalIntervals: 0,
                            suspiciousTiming: false,
                            timePatterns: [],
                        },
                        amountAnalysis: {
                            totalAmount: 0,
                            averageAmount: 0,
                            maxAmount: 0,
                            minAmount: 0,
                            roundAmountsCount: 0,
                            similarAmountsCount: 0,
                            potentialStructuring: false,
                            potentialSplitting: false,
                        },
                        addressReuse: {
                            uniqueFromAddresses: 0,
                            uniqueToAddresses: 0,
                            reusedFromAddresses: {},
                            reusedToAddresses: {},
                            addressReuseDetected: false,
                        },
                        clusteringHints: {
                            totalClusters: 0,
                            potentialClusters: 0,
                            clusterDetails: {},
                            clusteringDetected: false,
                        },
                        riskIndicators: {
                            riskScore: 0,
                            riskLevel: 'LOW',
                            riskFactors: [],
                            totalAmount: 0,
                            transactionCount: 0,
                            maxDepth: 0,
                        },
                    },
                },
                investigationSummary: {
                    status: 'completed',
                    keyMetrics: {
                        totalTransactions: 0,
                        totalAmountBtc: 0,
                        uniqueAddresses: 0,
                        maximumDepth: 0,
                        riskLevel: 'LOW',
                    },
                    keyConcerns: [],
                    investigationQuality: {
                        qualityScore: 0,
                        qualityLevel: 'LOW',
                        qualityFactors: [],
                        completenessPercentage: 0,
                    },
                    nextSteps: [],
                    timeline: [],
                },
                evidencePackage: [],
                auditTrail: [],
            };
            await expect(reportingService.generateReports(mockResults)).rejects.toThrow();
        });
        it('should validate input parameters', async () => {
            const invalidInput = {
                initialTxid: 'invalid-txid',
                targetAddress: 'invalid-address',
                maxDepth: -1,
                victimName: '',
                caseDescription: '',
                scamAmount: -1,
            };
            await expect(investigator.startInvestigation(invalidInput)).rejects.toThrow();
        });
    });
    describe('Performance Integration', () => {
        it('should handle large datasets efficiently', async () => {
            const largeResults = {
                investigationId: 'large-test',
                timestamp: '2024-01-15T10:30:00.000Z',
                inputParameters: {
                    initialTxid: 'a1b2c3d4e5f67890123456789012345678901234567890123456789012345678',
                    targetAddress: '**********************************',
                    maxDepth: 5,
                    victimName: 'Large Test',
                    caseDescription: 'Performance test',
                },
                basicResults: {
                    transactionCount: 1000,
                    totalAmount: 100.0,
                    addressCount: 500,
                },
                detailedTransactions: Array.from({ length: 1000 }, (_, i) => ({
                    txid: `tx${i.toString().padStart(60, '0')}abcdef`,
                    fromAddress: `1Address${i}`,
                    toAddress: `1Address${i + 1}`,
                    amountBtc: 0.1,
                    depth: Math.floor(i / 200) + 1,
                    timestamp: new Date(Date.now() + i * 1000).toISOString(),
                    blockHeight: 800000 + i,
                    confirmations: true,
                    investigationId: 'large-test',
                    fees: 0.0001,
                })),
                addressAnalysis: Array.from({ length: 500 }, (_, i) => ({
                    address: `1Address${i}`,
                    addressType: 'legacy',
                    totalReceived: 0.2,
                    totalSent: 0.1,
                    balance: 0.1,
                    transactionCount: 2,
                    firstSeen: new Date(Date.now() + i * 1000).toISOString(),
                    lastSeen: new Date(Date.now() + i * 2000).toISOString(),
                    riskScore: Math.floor(Math.random() * 10),
                })),
                advancedAnalysis: {
                    riskAssessment: {
                        finalRiskLevel: 'MEDIUM',
                        compositeRiskScore: 5.0,
                        baseRiskScore: 4.0,
                        suspicionScore: 4.0,
                        riskFactors: ['high_transaction_volume', 'mixing_services'],
                        suspiciousActivities: ['mixing_services', 'exchange_deposits'],
                        recommendations: ['Monitor activity'],
                    },
                    suspiciousActivity: {
                        overallSuspicionScore: 4.0,
                        suspicionLevel: 'MEDIUM',
                        mixingServices: { detected: true, severity: 3, confidence: 0.7, details: {} },
                        exchangeDeposits: { detected: true, severity: 2, confidence: 0.8, details: {} },
                        peelChains: { detected: false, severity: 0, confidence: 0, details: {} },
                        consolidationPatterns: { detected: true, severity: 1, confidence: 0.6, details: {} },
                        privacyCoinInteractions: { detected: false, severity: 0, confidence: 0, details: {} },
                    },
                    transactionPatterns: {
                        timingAnalysis: {
                            averageIntervalSeconds: 1000,
                            rapidSuccessionCount: 50,
                            totalIntervals: 999,
                            suspiciousTiming: true,
                            timePatterns: ['rapid_succession'],
                        },
                        amountAnalysis: {
                            totalAmount: 100.0,
                            averageAmount: 0.1,
                            maxAmount: 0.1,
                            minAmount: 0.1,
                            roundAmountsCount: 1000,
                            similarAmountsCount: 1000,
                            potentialStructuring: true,
                            potentialSplitting: false,
                        },
                        addressReuse: {
                            uniqueFromAddresses: 500,
                            uniqueToAddresses: 500,
                            reusedFromAddresses: {},
                            reusedToAddresses: {},
                            addressReuseDetected: false,
                        },
                        clusteringHints: {
                            totalClusters: 10,
                            potentialClusters: 5,
                            clusterDetails: {},
                            clusteringDetected: true,
                        },
                        riskIndicators: {
                            riskScore: 5.0,
                            riskLevel: 'MEDIUM',
                            riskFactors: ['high_transaction_volume', 'mixing_services'],
                            totalAmount: 100.0,
                            transactionCount: 1000,
                            maxDepth: 5,
                        },
                    },
                },
                investigationSummary: {
                    status: 'completed',
                    keyMetrics: {
                        totalTransactions: 1000,
                        totalAmountBtc: 100.0,
                        uniqueAddresses: 500,
                        maximumDepth: 5,
                        riskLevel: 'MEDIUM',
                    },
                    keyConcerns: ['Large scale investigation'],
                    investigationQuality: {
                        qualityScore: 75,
                        qualityLevel: 'HIGH',
                        qualityFactors: ['complete_transaction_trace', 'high_volume'],
                        completenessPercentage: 75,
                    },
                    nextSteps: ['Detailed analysis required'],
                    timeline: [],
                },
                evidencePackage: [],
                auditTrail: [],
            };
            const startTime = Date.now();
            await visualizationService.generateVisualizations(largeResults, 'large-test');
            const endTime = Date.now();
            const duration = endTime - startTime;
            // Should complete within reasonable time (10 seconds)
            expect(duration).toBeLessThan(10000);
        });
    });
    describe('Security Integration', () => {
        it('should sanitize file paths', async () => {
            const maliciousResults = {
                investigationId: '../../../malicious',
                timestamp: '2024-01-15T10:30:00.000Z',
                inputParameters: {
                    initialTxid: 'a1b2c3d4e5f67890123456789012345678901234567890123456789012345678',
                    targetAddress: '**********************************',
                    maxDepth: 1,
                    victimName: '<script>alert("xss")</script>',
                    caseDescription: 'Security test',
                },
                basicResults: {
                    transactionCount: 0,
                    totalAmount: 0,
                    addressCount: 0,
                },
                detailedTransactions: [],
                addressAnalysis: [],
                advancedAnalysis: {
                    riskAssessment: {
                        finalRiskLevel: 'LOW',
                        compositeRiskScore: 0,
                        baseRiskScore: 0,
                        suspicionScore: 0,
                        riskFactors: [],
                        suspiciousActivities: [],
                        recommendations: [],
                    },
                    suspiciousActivity: {
                        overallSuspicionScore: 0,
                        suspicionLevel: 'LOW',
                        mixingServices: { detected: false, severity: 0, confidence: 0, details: {} },
                        exchangeDeposits: { detected: false, severity: 0, confidence: 0, details: {} },
                        peelChains: { detected: false, severity: 0, confidence: 0, details: {} },
                        consolidationPatterns: { detected: false, severity: 0, confidence: 0, details: {} },
                        privacyCoinInteractions: { detected: false, severity: 0, confidence: 0, details: {} },
                    },
                    transactionPatterns: {
                        timingAnalysis: {
                            averageIntervalSeconds: 0,
                            rapidSuccessionCount: 0,
                            totalIntervals: 0,
                            suspiciousTiming: false,
                            timePatterns: [],
                        },
                        amountAnalysis: {
                            totalAmount: 0,
                            averageAmount: 0,
                            maxAmount: 0,
                            minAmount: 0,
                            roundAmountsCount: 0,
                            similarAmountsCount: 0,
                            potentialStructuring: false,
                            potentialSplitting: false,
                        },
                        addressReuse: {
                            uniqueFromAddresses: 0,
                            uniqueToAddresses: 0,
                            reusedFromAddresses: {},
                            reusedToAddresses: {},
                            addressReuseDetected: false,
                        },
                        clusteringHints: {
                            totalClusters: 0,
                            potentialClusters: 0,
                            clusterDetails: {},
                            clusteringDetected: false,
                        },
                        riskIndicators: {
                            riskScore: 0,
                            riskLevel: 'LOW',
                            riskFactors: [],
                            totalAmount: 0,
                            transactionCount: 0,
                            maxDepth: 0,
                        },
                    },
                },
                investigationSummary: {
                    status: 'completed',
                    keyMetrics: {
                        totalTransactions: 0,
                        totalAmountBtc: 0,
                        uniqueAddresses: 0,
                        maximumDepth: 0,
                        riskLevel: 'LOW',
                    },
                    keyConcerns: [],
                    investigationQuality: {
                        qualityScore: 0,
                        qualityLevel: 'LOW',
                        qualityFactors: [],
                        completenessPercentage: 0,
                    },
                    nextSteps: [],
                    timeline: [],
                },
                evidencePackage: [],
                auditTrail: [],
            };
            // Should not throw errors and should sanitize the investigation ID
            const result = await visualizationService.generateVisualizations(maliciousResults, 'sanitized-id');
            expect(result).toBeDefined();
            // Verify that file paths don't contain malicious content
            const writeFileCalls = mockFs.writeFileSync.mock.calls;
            writeFileCalls.forEach(call => {
                const filePath = call[0];
                expect(filePath).not.toContain('../');
                expect(filePath).not.toContain('<script>');
            });
        });
    });
});
//# sourceMappingURL=integration.test.js.map