"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const validation_1 = require("../utils/validation");
describe('Validation Utils', () => {
    describe('validateBitcoinAddress', () => {
        test('should validate legacy addresses', () => {
            expect((0, validation_1.validateBitcoinAddress)('**********************************')).toBe(true);
            expect((0, validation_1.validateBitcoinAddress)('**********************************')).toBe(true);
        });
        test('should validate SegWit addresses', () => {
            expect((0, validation_1.validateBitcoinAddress)('**********************************')).toBe(true);
        });
        test('should validate Bech32 addresses', () => {
            expect((0, validation_1.validateBitcoinAddress)('******************************************')).toBe(true);
        });
        test('should validate Taproot addresses', () => {
            expect((0, validation_1.validateBitcoinAddress)('**************************************************************')).toBe(true);
        });
        test('should reject invalid addresses', () => {
            expect((0, validation_1.validateBitcoinAddress)('')).toBe(false);
            expect((0, validation_1.validateBitcoinAddress)('invalid')).toBe(false);
            expect((0, validation_1.validateBitcoinAddress)('1234567890')).toBe(false);
            expect((0, validation_1.validateBitcoinAddress)('bc1invalid')).toBe(false);
        });
    });
    describe('getBitcoinAddressType', () => {
        test('should identify legacy addresses', () => {
            expect((0, validation_1.getBitcoinAddressType)('**********************************')).toBe('legacy');
        });
        test('should identify SegWit addresses', () => {
            expect((0, validation_1.getBitcoinAddressType)('******************************************')).toBe('segwit');
        });
        test('should identify Taproot addresses', () => {
            expect((0, validation_1.getBitcoinAddressType)('**************************************************************')).toBe('taproot');
        });
        test('should return unknown for invalid addresses', () => {
            expect((0, validation_1.getBitcoinAddressType)('invalid')).toBe('unknown');
        });
    });
    describe('validateTransactionId', () => {
        test('should validate correct transaction IDs', () => {
            expect((0, validation_1.validateTransactionId)('a1b2c3d4e5f67890123456789012345678901234567890123456789012345678')).toBe(true);
            expect((0, validation_1.validateTransactionId)('0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef')).toBe(true);
        });
        test('should reject invalid transaction IDs', () => {
            expect((0, validation_1.validateTransactionId)('')).toBe(false);
            expect((0, validation_1.validateTransactionId)('short')).toBe(false);
            expect((0, validation_1.validateTransactionId)('a1b2c3d4e5f67890123456789012345678901234567890123456789012345678x')).toBe(false); // 65 chars
            expect((0, validation_1.validateTransactionId)('g1b2c3d4e5f67890123456789012345678901234567890123456789012345678')).toBe(false); // invalid char
        });
    });
    describe('validateDepth', () => {
        test('should validate correct depths', () => {
            expect((0, validation_1.validateDepth)(1)).toBe(true);
            expect((0, validation_1.validateDepth)(5)).toBe(true);
            expect((0, validation_1.validateDepth)(10)).toBe(true);
        });
        test('should reject invalid depths', () => {
            expect((0, validation_1.validateDepth)(0)).toBe(false);
            expect((0, validation_1.validateDepth)(11)).toBe(false);
            expect((0, validation_1.validateDepth)(-1)).toBe(false);
            expect((0, validation_1.validateDepth)(1.5)).toBe(false);
        });
    });
    describe('validateBtcAmount', () => {
        test('should validate correct amounts', () => {
            expect((0, validation_1.validateBtcAmount)(0.1)).toBe(true);
            expect((0, validation_1.validateBtcAmount)(1)).toBe(true);
            expect((0, validation_1.validateBtcAmount)(21000000)).toBe(true);
        });
        test('should reject invalid amounts', () => {
            expect((0, validation_1.validateBtcAmount)(0)).toBe(false);
            expect((0, validation_1.validateBtcAmount)(-1)).toBe(false);
            expect((0, validation_1.validateBtcAmount)(21000001)).toBe(false);
        });
    });
});
//# sourceMappingURL=validation.test.js.map