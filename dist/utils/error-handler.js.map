{"version": 3, "file": "error-handler.js", "sourceRoot": "", "sources": ["../../src/utils/error-handler.ts"], "names": [], "mappings": ";;;AA+TA,4DA4BC;AA3VD,qCAAkC;AAElC,IAAY,SAkCX;AAlCD,WAAY,SAAS;IACnB,oBAAoB;IACpB,gEAAmD,CAAA;IACnD,8DAAiD,CAAA;IACjD,kEAAqD,CAAA;IAErD,aAAa;IACb,sDAAyC,CAAA;IACzC,kDAAqC,CAAA;IACrC,wCAA2B,CAAA;IAC3B,kDAAqC,CAAA;IAErC,uBAAuB;IACvB,0DAA6C,CAAA;IAC7C,4DAA+C,CAAA;IAC/C,oDAAuC,CAAA;IACvC,oDAAuC,CAAA;IAEvC,qBAAqB;IACrB,8CAAiC,CAAA;IACjC,kDAAqC,CAAA;IACrC,gDAAmC,CAAA;IACnC,8DAAiD,CAAA;IAEjD,kBAAkB;IAClB,sDAAyC,CAAA;IACzC,wDAA2C,CAAA;IAC3C,0DAA6C,CAAA;IAE7C,gBAAgB;IAChB,4DAA+C,CAAA;IAC/C,4CAA+B,CAAA;IAC/B,wDAA2C,CAAA;IAC3C,4CAA+B,CAAA;AACjC,CAAC,EAlCW,SAAS,yBAAT,SAAS,QAkCpB;AAaD,MAAa,cAAe,SAAQ,KAAK;IAKvC,YACE,IAAe,EACf,OAAe,EACf,OAA6B,EAC7B,mBAA4B,EAC5B,gBAAyB,IAAI;QAE7B,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,IAAI,GAAG,gBAAgB,CAAC;QAC7B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QAEnC,IAAI,CAAC,OAAO,GAAG;YACb,IAAI;YACJ,OAAO;YACP,OAAO;YACP,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,mBAAmB,EAAE,mBAAmB,IAAI,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC;YAC5E,WAAW,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;YACrC,SAAS,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;SAClC,CAAC;QAEF,sBAAsB;QACtB,KAAK,CAAC,iBAAiB,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;IAChD,CAAC;IAEO,qBAAqB,CAAC,IAAe;QAC3C,MAAM,YAAY,GAA8B;YAC9C,CAAC,SAAS,CAAC,uBAAuB,CAAC,EACjC,4EAA4E;YAC9E,CAAC,SAAS,CAAC,sBAAsB,CAAC,EAChC,2EAA2E;YAC7E,CAAC,SAAS,CAAC,wBAAwB,CAAC,EAClC,8EAA8E;YAChF,CAAC,SAAS,CAAC,kBAAkB,CAAC,EAC5B,oFAAoF;YACtF,CAAC,SAAS,CAAC,gBAAgB,CAAC,EAC1B,sEAAsE;YACxE,CAAC,SAAS,CAAC,WAAW,CAAC,EAAE,0DAA0D;YACnF,CAAC,SAAS,CAAC,gBAAgB,CAAC,EAC1B,4EAA4E;YAC9E,CAAC,SAAS,CAAC,oBAAoB,CAAC,EAC9B,gFAAgF;YAClF,CAAC,SAAS,CAAC,qBAAqB,CAAC,EAC/B,4DAA4D;YAC9D,CAAC,SAAS,CAAC,iBAAiB,CAAC,EAC3B,uEAAuE;YACzE,CAAC,SAAS,CAAC,iBAAiB,CAAC,EAAE,0DAA0D;YACzF,CAAC,SAAS,CAAC,cAAc,CAAC,EAAE,qCAAqC;YACjE,CAAC,SAAS,CAAC,gBAAgB,CAAC,EAC1B,0EAA0E;YAC5E,CAAC,SAAS,CAAC,eAAe,CAAC,EAAE,gEAAgE;YAC7F,CAAC,SAAS,CAAC,sBAAsB,CAAC,EAChC,kEAAkE;YACpE,CAAC,SAAS,CAAC,kBAAkB,CAAC,EAC5B,gEAAgE;YAClE,CAAC,SAAS,CAAC,mBAAmB,CAAC,EAAE,uDAAuD;YACxF,CAAC,SAAS,CAAC,oBAAoB,CAAC,EAC9B,gEAAgE;YAClE,CAAC,SAAS,CAAC,qBAAqB,CAAC,EAC/B,iFAAiF;YACnF,CAAC,SAAS,CAAC,aAAa,CAAC,EAAE,4DAA4D;YACvF,CAAC,SAAS,CAAC,mBAAmB,CAAC,EAC7B,4EAA4E;YAC9E,CAAC,SAAS,CAAC,aAAa,CAAC,EACvB,oEAAoE;SACvE,CAAC;QAEF,OAAO,CACL,YAAY,CAAC,IAAI,CAAC,IAAI,oEAAoE,CAC3F,CAAC;IACJ,CAAC;IAEO,aAAa,CAAC,IAAe;QACnC,MAAM,iBAAiB,GAAG;YACxB,SAAS,CAAC,gBAAgB;YAC1B,SAAS,CAAC,WAAW;YACrB,SAAS,CAAC,gBAAgB;YAC1B,SAAS,CAAC,sBAAsB;YAChC,SAAS,CAAC,aAAa;SACxB,CAAC;QACF,OAAO,iBAAiB,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IAC1C,CAAC;IAEO,WAAW,CAAC,IAAe;QACjC,MAAM,eAAe,GAAG;YACtB,SAAS,CAAC,kBAAkB;YAC5B,SAAS,CAAC,gBAAgB;YAC1B,SAAS,CAAC,WAAW;YACrB,SAAS,CAAC,aAAa;SACxB,CAAC;QACF,OAAO,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IACxC,CAAC;CACF;AAnGD,wCAmGC;AAED,MAAa,YAAY;IAAzB;QAEU,gBAAW,GAA2B,IAAI,GAAG,EAAE,CAAC;QAChD,eAAU,GAAyB,IAAI,GAAG,EAAE,CAAC;IAiKvD,CAAC;IA/JQ,MAAM,CAAC,WAAW;QACvB,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC;YAC3B,YAAY,CAAC,QAAQ,GAAG,IAAI,YAAY,EAAE,CAAC;QAC7C,CAAC;QACD,OAAO,YAAY,CAAC,QAAQ,CAAC;IAC/B,CAAC;IAED;;OAEG;IACI,WAAW,CAAC,KAA6B,EAAE,OAA6B;QAC7E,IAAI,YAA0B,CAAC;QAE/B,IAAI,KAAK,YAAY,cAAc,EAAE,CAAC;YACpC,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC;QAC/B,CAAC;aAAM,CAAC;YACN,2CAA2C;YAC3C,MAAM,cAAc,GAAG,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC,CAAC;YAC3D,YAAY,GAAG,cAAc,CAAC,OAAO,CAAC;QACxC,CAAC;QAED,yBAAyB;QACzB,IAAI,OAAO,EAAE,CAAC;YACZ,YAAY,CAAC,OAAO,GAAG,EAAE,GAAG,YAAY,CAAC,OAAO,EAAE,GAAG,OAAO,EAAE,CAAC;QACjE,CAAC;QAED,wBAAwB;QACxB,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;QAEnC,gBAAgB;QAChB,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;QAE5B,OAAO,YAAY,CAAC;IACtB,CAAC;IAED;;OAEG;IACK,uBAAuB,CAAC,KAAY;QAC1C,IAAI,IAAI,GAAG,SAAS,CAAC,aAAa,CAAC;QACnC,IAAI,aAAa,GAAG,KAAK,CAAC;QAE1B,yDAAyD;QACzD,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,gBAAgB,CAAC,EAAE,CAAC;YACjF,IAAI,GAAG,SAAS,CAAC,cAAc,CAAC;YAChC,aAAa,GAAG,IAAI,CAAC;QACvB,CAAC;aAAM,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,mBAAmB,CAAC,EAAE,CAAC;YAC3F,IAAI,GAAG,SAAS,CAAC,mBAAmB,CAAC;YACrC,aAAa,GAAG,IAAI,CAAC;QACvB,CAAC;aAAM,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;YAC7C,IAAI,GAAG,SAAS,CAAC,aAAa,CAAC;YAC/B,aAAa,GAAG,IAAI,CAAC;QACvB,CAAC;aAAM,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC;YACvF,IAAI,GAAG,SAAS,CAAC,kBAAkB,CAAC;YACpC,aAAa,GAAG,IAAI,CAAC;QACvB,CAAC;QAED,OAAO,IAAI,cAAc,CACvB,IAAI,EACJ,KAAK,CAAC,OAAO,EACb,EAAE,aAAa,EAAE,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,EAAE,EACjD,SAAS,EACT,aAAa,CACd,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,UAAU,CAAC,IAAe;QAChC,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC9C,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;QACtC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,IAAI,EAAE,CAAC,CAAC;IACxC,CAAC;IAED;;OAEG;IACK,QAAQ,CAAC,YAA0B;QACzC,MAAM,OAAO,GAAG;YACd,IAAI,EAAE,YAAY,CAAC,IAAI;YACvB,OAAO,EAAE,YAAY,CAAC,OAAO;YAC7B,OAAO,EAAE,YAAY,CAAC,OAAO;YAC7B,SAAS,EAAE,YAAY,CAAC,SAAS;YACjC,WAAW,EAAE,YAAY,CAAC,WAAW;YACrC,SAAS,EAAE,YAAY,CAAC,SAAS;SAClC,CAAC;QAEF,IAAI,YAAY,CAAC,WAAW,EAAE,CAAC;YAC7B,eAAM,CAAC,IAAI,CAAC,4BAA4B,EAAE,OAAO,CAAC,CAAC;QACrD,CAAC;aAAM,CAAC;YACN,eAAM,CAAC,KAAK,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC;QAC1C,CAAC;IACH,CAAC;IAED;;OAEG;IACI,aAAa;QAKlB,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAC9D,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,EAC3B,CAAC,CACF,CAAC;QAEF,MAAM,YAAY,GAA2B,EAAE,CAAC;QAChD,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,IAAI,EAAE,EAAE;YACvC,YAAY,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;QAC7B,CAAC,CAAC,CAAC;QAEH,MAAM,YAAY,GAA2B,EAAE,CAAC;QAChD,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE;YACrC,YAAY,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QAC1C,CAAC,CAAC,CAAC;QAEH,OAAO;YACL,WAAW;YACX,YAAY;YACZ,YAAY;SACb,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,UAAU;QACf,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;QACzB,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;IAC1B,CAAC;IAED;;OAEG;IACI,WAAW,CAAC,IAAe;QAChC,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC9C,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAE5C,mBAAmB;QACnB,MAAM,gBAAgB,GAA8B;YAClD,CAAC,SAAS,CAAC,kBAAkB,CAAC,EAAE,CAAC;YACjC,CAAC,SAAS,CAAC,kBAAkB,CAAC,EAAE,CAAC;YACjC,CAAC,SAAS,CAAC,oBAAoB,CAAC,EAAE,CAAC;YACnC,CAAC,SAAS,CAAC,qBAAqB,CAAC,EAAE,CAAC;YACpC,CAAC,SAAS,CAAC,aAAa,CAAC,EAAE,EAAE;SACvB,CAAC;QAET,MAAM,SAAS,GAAG,gBAAgB,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;QAE/C,yDAAyD;QACzD,IAAI,KAAK,IAAI,SAAS,IAAI,SAAS,EAAE,CAAC;YACpC,MAAM,UAAU,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;YACzD,OAAO,SAAS,GAAG,UAAU,CAAC;QAChC,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;CACF;AApKD,oCAoKC;AAED;;GAEG;AACH,SAAgB,wBAAwB;IACtC,MAAM,YAAY,GAAG,YAAY,CAAC,WAAW,EAAE,CAAC;IAEhD,OAAO,CAAC,EAAE,CAAC,mBAAmB,EAAE,CAAC,KAAY,EAAE,EAAE;QAC/C,MAAM,OAAO,GAAG,YAAY,CAAC,WAAW,CAAC,KAAK,EAAE,EAAE,MAAM,EAAE,mBAAmB,EAAE,CAAC,CAAC;QAEjF,OAAO,CAAC,KAAK,CAAC,qBAAqB,EAAE,OAAO,CAAC,mBAAmB,CAAC,CAAC;QAElE,6CAA6C;QAC7C,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;YACzB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,OAAO,CAAC,EAAE,CAAC,oBAAoB,EAAE,CAAC,MAAW,EAAE,OAAqB,EAAE,EAAE;QACtE,MAAM,KAAK,GAAG,MAAM,YAAY,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;QAC3E,MAAM,OAAO,GAAG,YAAY,CAAC,WAAW,CAAC,KAAK,EAAE;YAC9C,MAAM,EAAE,oBAAoB;YAC5B,OAAO,EAAE,OAAO,CAAC,QAAQ,EAAE;SAC5B,CAAC,CAAC;QAEH,OAAO,CAAC,KAAK,CAAC,sBAAsB,EAAE,OAAO,CAAC,mBAAmB,CAAC,CAAC;QAEnE,6CAA6C;QAC7C,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;YACzB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC;IACH,CAAC,CAAC,CAAC;AACL,CAAC;AAED;;GAEG;AACU,QAAA,UAAU,GAAG;IACxB;;OAEG;IACH,UAAU,EAAE,CAAC,OAAe,EAAE,OAA6B,EAAE,EAAE,CAC7D,IAAI,cAAc,CAAC,SAAS,CAAC,wBAAwB,EAAE,OAAO,EAAE,OAAO,CAAC;IAE1E;;OAEG;IACH,GAAG,EAAE,CAAC,OAAe,EAAE,OAA6B,EAAE,EAAE,CACtD,IAAI,cAAc,CAAC,SAAS,CAAC,kBAAkB,EAAE,OAAO,EAAE,OAAO,CAAC;IAEpE;;OAEG;IACH,UAAU,EAAE,CAAC,OAAe,EAAE,OAA6B,EAAE,EAAE,CAC7D,IAAI,cAAc,CAAC,SAAS,CAAC,gBAAgB,EAAE,OAAO,EAAE,OAAO,CAAC;IAElE;;OAEG;IACH,QAAQ,EAAE,CAAC,OAAe,EAAE,OAA6B,EAAE,EAAE,CAC3D,IAAI,cAAc,CAAC,SAAS,CAAC,kBAAkB,EAAE,OAAO,EAAE,OAAO,CAAC;IAEpE;;OAEG;IACH,SAAS,EAAE,CACT,EAA8B,EAC9B,YAAuB,SAAS,CAAC,aAAa,EAC9C,EAAE;QACF,OAAO,KAAK,EAAE,GAAG,IAAO,EAAc,EAAE;YACtC,IAAI,CAAC;gBACH,OAAO,MAAM,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC;YAC3B,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,KAAK,YAAY,cAAc,EAAE,CAAC;oBACpC,MAAM,KAAK,CAAC;gBACd,CAAC;gBACD,MAAM,IAAI,cAAc,CACtB,SAAS,EACT,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EACtD,EAAE,aAAa,EAAE,KAAK,EAAE,CACzB,CAAC;YACJ,CAAC;QACH,CAAC,CAAC;IACJ,CAAC;CACF,CAAC"}