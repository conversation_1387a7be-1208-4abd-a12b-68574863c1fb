{"version": 3, "file": "security.js", "sourceRoot": "", "sources": ["../../src/utils/security.ts"], "names": [], "mappings": ";;;;;;AAAA,oDAA4B;AAC5B,gDAAwB;AACxB,mDAA4D;AAC5D,qCAAkC;AAYrB,QAAA,uBAAuB,GAAmB;IACrD,WAAW,EAAE,GAAG,GAAG,IAAI,GAAG,IAAI,EAAE,QAAQ;IACxC,qBAAqB,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,CAAC;IACjE,cAAc,EAAE,KAAK;IACrB,eAAe,EAAE,EAAE,GAAG,IAAI,EAAE,WAAW;IACvC,oBAAoB,EAAE,GAAG;IACzB,uBAAuB,EAAE,IAAI;IAC7B,mBAAmB,EAAE,IAAI;CAC1B,CAAC;AAEF,MAAa,iBAAiB;IAI5B,YAAY,SAAyB,+BAAuB;QAFpD,kBAAa,GAAwD,IAAI,GAAG,EAAE,CAAC;QAGrF,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;IAED;;OAEG;IACH,sBAAsB,CAAC,OAAe;QACpC,IAAI,CAAC,OAAO,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;YAC5C,MAAM,IAAI,8BAAc,CACtB,yBAAS,CAAC,uBAAuB,EACjC,4CAA4C,CAC7C,CAAC;QACJ,CAAC;QAED,4DAA4D;QAC5D,MAAM,YAAY,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAEpC,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC9B,MAAM,IAAI,8BAAc,CACtB,yBAAS,CAAC,uBAAuB,EACjC,iCAAiC,CAClC,CAAC;QACJ,CAAC;QAED,IAAI,YAAY,CAAC,MAAM,GAAG,GAAG,EAAE,CAAC;YAC9B,MAAM,IAAI,8BAAc,CAAC,yBAAS,CAAC,uBAAuB,EAAE,6BAA6B,CAAC,CAAC;QAC7F,CAAC;QAED,kCAAkC;QAClC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC;YACzC,MAAM,IAAI,8BAAc,CACtB,yBAAS,CAAC,uBAAuB,EACjC,6CAA6C,CAC9C,CAAC;QACJ,CAAC;QAED,OAAO,YAAY,CAAC;IACtB,CAAC;IAED;;OAEG;IACH,qBAAqB,CAAC,IAAY;QAChC,IAAI,CAAC,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;YACtC,MAAM,IAAI,8BAAc,CACtB,yBAAS,CAAC,sBAAsB,EAChC,2CAA2C,CAC5C,CAAC;QACJ,CAAC;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;QAE5C,IAAI,SAAS,CAAC,MAAM,KAAK,EAAE,EAAE,CAAC;YAC5B,MAAM,IAAI,8BAAc,CACtB,yBAAS,CAAC,sBAAsB,EAChC,8CAA8C,CAC/C,CAAC;QACJ,CAAC;QAED,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;YACtC,MAAM,IAAI,8BAAc,CACtB,yBAAS,CAAC,sBAAsB,EAChC,yDAAyD,CAC1D,CAAC;QACJ,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;OAEG;IACH,mBAAmB,CAAC,KAAa,EAAE,SAAiB,EAAE,SAAkB;QACtE,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;YAC9B,MAAM,IAAI,8BAAc,CAAC,yBAAS,CAAC,wBAAwB,EAAE,GAAG,SAAS,mBAAmB,CAAC,CAAC;QAChG,CAAC;QAED,MAAM,MAAM,GAAG,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC;QACvD,IAAI,KAAK,CAAC,MAAM,GAAG,MAAM,EAAE,CAAC;YAC1B,MAAM,IAAI,8BAAc,CACtB,yBAAS,CAAC,wBAAwB,EAClC,GAAG,SAAS,8BAA8B,MAAM,aAAa,CAC9D,CAAC;QACJ,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,CAAC,uBAAuB,EAAE,CAAC;YACxC,OAAO,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;QACpC,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACH,oBAAoB,CAAC,KAAU,EAAE,SAAiB,EAAE,GAAY,EAAE,GAAY;QAC5E,MAAM,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;QAE1B,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;YACjC,MAAM,IAAI,8BAAc,CACtB,yBAAS,CAAC,wBAAwB,EAClC,GAAG,SAAS,yBAAyB,CACtC,CAAC;QACJ,CAAC;QAED,IAAI,GAAG,KAAK,SAAS,IAAI,GAAG,GAAG,GAAG,EAAE,CAAC;YACnC,MAAM,IAAI,8BAAc,CACtB,yBAAS,CAAC,wBAAwB,EAClC,GAAG,SAAS,qBAAqB,GAAG,EAAE,CACvC,CAAC;QACJ,CAAC;QAED,IAAI,GAAG,KAAK,SAAS,IAAI,GAAG,GAAG,GAAG,EAAE,CAAC;YACnC,MAAM,IAAI,8BAAc,CACtB,yBAAS,CAAC,wBAAwB,EAClC,GAAG,SAAS,oBAAoB,GAAG,EAAE,CACtC,CAAC;QACJ,CAAC;QAED,OAAO,GAAG,CAAC;IACb,CAAC;IAED;;OAEG;IACH,gBAAgB,CAAC,QAAgB,EAAE,aAAsB;QACvD,IAAI,CAAC,QAAQ,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE,CAAC;YAC9C,MAAM,IAAI,8BAAc,CACtB,yBAAS,CAAC,wBAAwB,EAClC,sCAAsC,CACvC,CAAC;QACJ,CAAC;QAED,MAAM,SAAS,GAAG,cAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QAE3C,oCAAoC;QACpC,IAAI,IAAI,CAAC,MAAM,CAAC,mBAAmB,IAAI,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;YAChE,MAAM,IAAI,8BAAc,CACtB,yBAAS,CAAC,kBAAkB,EAC5B,sCAAsC,CACvC,CAAC;QACJ,CAAC;QAED,0BAA0B;QAC1B,MAAM,GAAG,GAAG,cAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC;QAClD,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;YAC5D,MAAM,IAAI,8BAAc,CACtB,yBAAS,CAAC,kBAAkB,EAC5B,kBAAkB,GAAG,iBAAiB,CACvC,CAAC;QACJ,CAAC;QAED,oDAAoD;QACpD,IAAI,aAAa,EAAE,CAAC;YAClB,MAAM,YAAY,GAAG,cAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YAC7C,MAAM,YAAY,GAAG,cAAI,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;YAEjD,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE,CAAC;gBAC3C,MAAM,IAAI,8BAAc,CACtB,yBAAS,CAAC,kBAAkB,EAC5B,wCAAwC,CACzC,CAAC;YACJ,CAAC;QACH,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,UAAkB;QAC/B,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAElD,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,WAAW,EAAE,GAAG,EAAE,CAAC,CAAC;YACnE,OAAO,IAAI,CAAC;QACd,CAAC;QAED,iCAAiC;QACjC,IAAI,GAAG,GAAG,MAAM,CAAC,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE,CAAC;YAC3D,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,WAAW,EAAE,GAAG,EAAE,CAAC,CAAC;YACnE,OAAO,IAAI,CAAC;QACd,CAAC;QAED,oCAAoC;QACpC,IAAI,MAAM,CAAC,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,oBAAoB,EAAE,CAAC;YACrD,eAAM,CAAC,IAAI,CAAC,qBAAqB,EAAE,EAAE,UAAU,EAAE,KAAK,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;YACxE,OAAO,KAAK,CAAC;QACf,CAAC;QAED,kBAAkB;QAClB,MAAM,CAAC,KAAK,EAAE,CAAC;QACf,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACK,cAAc,CAAC,KAAa;QAClC,OAAO,KAAK;aACT,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,6BAA6B;aAClD,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,gBAAgB;aACrC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,qBAAqB;aAC1C,OAAO,CAAC,kBAAkB,EAAE,EAAE,CAAC,CAAC,4BAA4B;aAC5D,IAAI,EAAE,CAAC;IACZ,CAAC;IAED;;OAEG;IACH,YAAY,CAAC,IAAqB;QAChC,OAAO,gBAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IAChE,CAAC;IAED;;OAEG;IACH,UAAU,CAAC,IAAqB,EAAE,YAAoB;QACpD,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;QAC3C,OAAO,UAAU,KAAK,YAAY,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,gBAAgB,CAAC,SAAiB,EAAE;QAClC,OAAO,gBAAM,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IACpD,CAAC;IAED;;OAEG;IACH,gBAAgB,CAAC,IAAY;QAC3B,IAAI,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;YACnC,MAAM,IAAI,8BAAc,CACtB,yBAAS,CAAC,kBAAkB,EAC5B,aAAa,IAAI,0CAA0C,IAAI,CAAC,MAAM,CAAC,WAAW,QAAQ,CAC3F,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,uBAAuB;QACrB,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,MAAM,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC;QAEjD,KAAK,MAAM,CAAC,UAAU,EAAE,MAAM,CAAC,IAAI,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,EAAE,CAAC;YAChE,IAAI,MAAM,CAAC,WAAW,GAAG,MAAM,EAAE,CAAC;gBAChC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YACxC,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACH,gBAAgB;QAKd,IAAI,aAAa,GAAG,CAAC,CAAC;QACtB,IAAI,eAAe,GAAG,CAAC,CAAC;QAExB,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC;YACjD,aAAa,IAAI,MAAM,CAAC,KAAK,CAAC;YAC9B,IAAI,MAAM,CAAC,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,oBAAoB,EAAE,CAAC;gBACrD,eAAe,EAAE,CAAC;YACpB,CAAC;QACH,CAAC;QAED,OAAO;YACL,gBAAgB,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI;YACzC,aAAa;YACb,eAAe;SAChB,CAAC;IACJ,CAAC;CACF;AA9RD,8CA8RC;AAED;;GAEG;AACH,MAAa,cAAc;IAGzB;;OAEG;IACH,MAAM,CAAC,2BAA2B,CAAC,MAMlC;QACC,IAAI,MAAM,CAAC,WAAW,EAAE,CAAC;YACvB,IAAI,CAAC,iBAAiB,CAAC,qBAAqB,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;QACnE,CAAC;QAED,IAAI,MAAM,CAAC,aAAa,EAAE,CAAC;YACzB,IAAI,CAAC,iBAAiB,CAAC,sBAAsB,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;QACtE,CAAC;QAED,IAAI,MAAM,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;YAClC,IAAI,CAAC,iBAAiB,CAAC,oBAAoB,CAAC,MAAM,CAAC,QAAQ,EAAE,UAAU,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;QAClF,CAAC;QAED,IAAI,MAAM,CAAC,UAAU,EAAE,CAAC;YACtB,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,MAAM,CAAC,UAAU,EAAE,YAAY,EAAE,GAAG,CAAC,CAAC;QACnF,CAAC;QAED,IAAI,MAAM,CAAC,eAAe,EAAE,CAAC;YAC3B,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,MAAM,CAAC,eAAe,EAAE,iBAAiB,EAAE,IAAI,CAAC,CAAC;QAC9F,CAAC;IACH,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,iBAAiB,CAAC,MAKxB;QACC,IAAI,MAAM,CAAC,UAAU,EAAE,CAAC;YACtB,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE,CAAC;gBAC9C,MAAM,IAAI,8BAAc,CAAC,yBAAS,CAAC,kBAAkB,EAAE,6BAA6B,CAAC,CAAC;YACxF,CAAC;QACH,CAAC;QAED,IAAI,MAAM,CAAC,cAAc,KAAK,SAAS,EAAE,CAAC;YACxC,IAAI,CAAC,iBAAiB,CAAC,oBAAoB,CACzC,MAAM,CAAC,cAAc,EACrB,gBAAgB,EAChB,IAAI,EACJ,MAAM,CACP,CAAC;QACJ,CAAC;QAED,IAAI,MAAM,CAAC,cAAc,KAAK,SAAS,EAAE,CAAC;YACxC,IAAI,CAAC,iBAAiB,CAAC,oBAAoB,CACzC,MAAM,CAAC,cAAc,EACrB,gBAAgB,EAChB,CAAC,EACD,KAAK,CACN,CAAC;QACJ,CAAC;QAED,IAAI,MAAM,CAAC,UAAU,KAAK,SAAS,EAAE,CAAC;YACpC,IAAI,CAAC,iBAAiB,CAAC,oBAAoB,CAAC,MAAM,CAAC,UAAU,EAAE,YAAY,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;QACtF,CAAC;IACH,CAAC;;AAtEH,wCAuEC;AAtEgB,gCAAiB,GAAG,IAAI,iBAAiB,EAAE,CAAC;AAwE7D;;GAEG;AACH,MAAa,kBAAkB;IAG7B;;OAEG;IACH,MAAM,CAAC,KAAK,CAAC,gBAAgB,CAC3B,SAA2B,EAC3B,UAAkB,EAClB,OAA6B;QAE7B,sBAAsB;QACtB,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,UAAU,CAAC,EAAE,CAAC;YAC/C,MAAM,IAAI,8BAAc,CAAC,yBAAS,CAAC,gBAAgB,EAAE,sCAAsC,EAAE;gBAC3F,UAAU;gBACV,OAAO;aACR,CAAC,CAAC;QACL,CAAC;QAED,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,SAAS,EAAE,CAAC;YACjC,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,IAAI,CAAC,4BAA4B,EAAE;gBACxC,UAAU;gBACV,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;gBAC7D,OAAO;aACR,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,iBAAiB;QACtB,OAAO,WAAW,CAChB,GAAG,EAAE;YACH,IAAI,CAAC,SAAS,CAAC,uBAAuB,EAAE,CAAC;QAC3C,CAAC,EACD,CAAC,GAAG,EAAE,GAAG,IAAI,CACd,CAAC,CAAC,2BAA2B;IAChC,CAAC;;AA1CH,gDA2CC;AA1CgB,4BAAS,GAAG,IAAI,iBAAiB,EAAE,CAAC;AA4CrD;;GAEG;AACU,QAAA,iBAAiB,GAAG,IAAI,iBAAiB,EAAE,CAAC"}