"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.securityValidator = exports.SecurityMiddleware = exports.InputValidator = exports.SecurityValidator = exports.DEFAULT_SECURITY_CONFIG = void 0;
const crypto_1 = __importDefault(require("crypto"));
const path_1 = __importDefault(require("path"));
const error_handler_1 = require("./error-handler");
const logger_1 = require("./logger");
exports.DEFAULT_SECURITY_CONFIG = {
    maxFileSize: 100 * 1024 * 1024, // 100MB
    allowedFileExtensions: ['.json', '.txt', '.html', '.pdf', '.csv'],
    maxInputLength: 10000,
    rateLimitWindow: 60 * 1000, // 1 minute
    maxRequestsPerWindow: 100,
    enableInputSanitization: true,
    enablePathTraversal: true,
};
class SecurityValidator {
    constructor(config = exports.DEFAULT_SECURITY_CONFIG) {
        this.requestCounts = new Map();
        this.config = config;
    }
    /**
     * Validate and sanitize Bitcoin address input
     */
    validateBitcoinAddress(address) {
        if (!address || typeof address !== 'string') {
            throw new error_handler_1.ForensicsError(error_handler_1.ErrorCode.INVALID_BITCOIN_ADDRESS, 'Bitcoin address must be a non-empty string');
        }
        // Remove whitespace and convert to lowercase for validation
        const cleanAddress = address.trim();
        if (cleanAddress.length === 0) {
            throw new error_handler_1.ForensicsError(error_handler_1.ErrorCode.INVALID_BITCOIN_ADDRESS, 'Bitcoin address cannot be empty');
        }
        if (cleanAddress.length > 100) {
            throw new error_handler_1.ForensicsError(error_handler_1.ErrorCode.INVALID_BITCOIN_ADDRESS, 'Bitcoin address is too long');
        }
        // Check for suspicious characters
        if (!/^[a-zA-Z0-9]+$/.test(cleanAddress)) {
            throw new error_handler_1.ForensicsError(error_handler_1.ErrorCode.INVALID_BITCOIN_ADDRESS, 'Bitcoin address contains invalid characters');
        }
        return cleanAddress;
    }
    /**
     * Validate and sanitize transaction ID input
     */
    validateTransactionId(txid) {
        if (!txid || typeof txid !== 'string') {
            throw new error_handler_1.ForensicsError(error_handler_1.ErrorCode.INVALID_TRANSACTION_ID, 'Transaction ID must be a non-empty string');
        }
        const cleanTxid = txid.trim().toLowerCase();
        if (cleanTxid.length !== 64) {
            throw new error_handler_1.ForensicsError(error_handler_1.ErrorCode.INVALID_TRANSACTION_ID, 'Transaction ID must be exactly 64 characters');
        }
        if (!/^[a-f0-9]{64}$/.test(cleanTxid)) {
            throw new error_handler_1.ForensicsError(error_handler_1.ErrorCode.INVALID_TRANSACTION_ID, 'Transaction ID must contain only hexadecimal characters');
        }
        return cleanTxid;
    }
    /**
     * Validate and sanitize general string input
     */
    validateStringInput(input, fieldName, maxLength) {
        if (typeof input !== 'string') {
            throw new error_handler_1.ForensicsError(error_handler_1.ErrorCode.INVALID_INPUT_PARAMETERS, `${fieldName} must be a string`);
        }
        const maxLen = maxLength || this.config.maxInputLength;
        if (input.length > maxLen) {
            throw new error_handler_1.ForensicsError(error_handler_1.ErrorCode.INVALID_INPUT_PARAMETERS, `${fieldName} exceeds maximum length of ${maxLen} characters`);
        }
        if (this.config.enableInputSanitization) {
            return this.sanitizeString(input);
        }
        return input;
    }
    /**
     * Validate numeric input
     */
    validateNumericInput(input, fieldName, min, max) {
        const num = Number(input);
        if (isNaN(num) || !isFinite(num)) {
            throw new error_handler_1.ForensicsError(error_handler_1.ErrorCode.INVALID_INPUT_PARAMETERS, `${fieldName} must be a valid number`);
        }
        if (min !== undefined && num < min) {
            throw new error_handler_1.ForensicsError(error_handler_1.ErrorCode.INVALID_INPUT_PARAMETERS, `${fieldName} must be at least ${min}`);
        }
        if (max !== undefined && num > max) {
            throw new error_handler_1.ForensicsError(error_handler_1.ErrorCode.INVALID_INPUT_PARAMETERS, `${fieldName} must be at most ${max}`);
        }
        return num;
    }
    /**
     * Validate file path for security
     */
    validateFilePath(filePath, baseDirectory) {
        if (!filePath || typeof filePath !== 'string') {
            throw new error_handler_1.ForensicsError(error_handler_1.ErrorCode.INVALID_INPUT_PARAMETERS, 'File path must be a non-empty string');
        }
        const cleanPath = path_1.default.normalize(filePath);
        // Check for path traversal attempts
        if (this.config.enablePathTraversal && cleanPath.includes('..')) {
            throw new error_handler_1.ForensicsError(error_handler_1.ErrorCode.SECURITY_VIOLATION, 'Path traversal detected in file path');
        }
        // Validate file extension
        const ext = path_1.default.extname(cleanPath).toLowerCase();
        if (ext && !this.config.allowedFileExtensions.includes(ext)) {
            throw new error_handler_1.ForensicsError(error_handler_1.ErrorCode.SECURITY_VIOLATION, `File extension ${ext} is not allowed`);
        }
        // Ensure path is within base directory if specified
        if (baseDirectory) {
            const resolvedPath = path_1.default.resolve(cleanPath);
            const resolvedBase = path_1.default.resolve(baseDirectory);
            if (!resolvedPath.startsWith(resolvedBase)) {
                throw new error_handler_1.ForensicsError(error_handler_1.ErrorCode.SECURITY_VIOLATION, 'File path is outside allowed directory');
            }
        }
        return cleanPath;
    }
    /**
     * Rate limiting check
     */
    checkRateLimit(identifier) {
        const now = Date.now();
        const record = this.requestCounts.get(identifier);
        if (!record) {
            this.requestCounts.set(identifier, { count: 1, windowStart: now });
            return true;
        }
        // Check if we're in a new window
        if (now - record.windowStart > this.config.rateLimitWindow) {
            this.requestCounts.set(identifier, { count: 1, windowStart: now });
            return true;
        }
        // Check if we've exceeded the limit
        if (record.count >= this.config.maxRequestsPerWindow) {
            logger_1.logger.warn('Rate limit exceeded', { identifier, count: record.count });
            return false;
        }
        // Increment count
        record.count++;
        return true;
    }
    /**
     * Sanitize string input
     */
    sanitizeString(input) {
        return input
            .replace(/[<>]/g, '') // Remove potential HTML tags
            .replace(/['"]/g, '') // Remove quotes
            .replace(/[\\]/g, '') // Remove backslashes
            .replace(/[\x00-\x1f\x7f]/g, '') // Remove control characters
            .trim();
    }
    /**
     * Generate secure hash for data integrity
     */
    generateHash(data) {
        return crypto_1.default.createHash('sha256').update(data).digest('hex');
    }
    /**
     * Verify data integrity using hash
     */
    verifyHash(data, expectedHash) {
        const actualHash = this.generateHash(data);
        return actualHash === expectedHash;
    }
    /**
     * Generate secure random ID
     */
    generateSecureId(length = 32) {
        return crypto_1.default.randomBytes(length).toString('hex');
    }
    /**
     * Validate file size
     */
    validateFileSize(size) {
        if (size > this.config.maxFileSize) {
            throw new error_handler_1.ForensicsError(error_handler_1.ErrorCode.SECURITY_VIOLATION, `File size ${size} bytes exceeds maximum allowed size of ${this.config.maxFileSize} bytes`);
        }
    }
    /**
     * Clean up rate limit records
     */
    cleanupRateLimitRecords() {
        const now = Date.now();
        const cutoff = now - this.config.rateLimitWindow;
        for (const [identifier, record] of this.requestCounts.entries()) {
            if (record.windowStart < cutoff) {
                this.requestCounts.delete(identifier);
            }
        }
    }
    /**
     * Get security statistics
     */
    getSecurityStats() {
        let totalRequests = 0;
        let blockedRequests = 0;
        for (const record of this.requestCounts.values()) {
            totalRequests += record.count;
            if (record.count >= this.config.maxRequestsPerWindow) {
                blockedRequests++;
            }
        }
        return {
            activeRateLimits: this.requestCounts.size,
            totalRequests,
            blockedRequests,
        };
    }
}
exports.SecurityValidator = SecurityValidator;
/**
 * Input validation decorators and utilities
 */
class InputValidator {
    /**
     * Validate investigation parameters
     */
    static validateInvestigationParams(params) {
        if (params.initialTxid) {
            this.securityValidator.validateTransactionId(params.initialTxid);
        }
        if (params.targetAddress) {
            this.securityValidator.validateBitcoinAddress(params.targetAddress);
        }
        if (params.maxDepth !== undefined) {
            this.securityValidator.validateNumericInput(params.maxDepth, 'maxDepth', 1, 20);
        }
        if (params.victimName) {
            this.securityValidator.validateStringInput(params.victimName, 'victimName', 100);
        }
        if (params.caseDescription) {
            this.securityValidator.validateStringInput(params.caseDescription, 'caseDescription', 1000);
        }
    }
    /**
     * Validate API configuration
     */
    static validateApiConfig(config) {
        if (config.apiBaseUrl) {
            if (!config.apiBaseUrl.startsWith('https://')) {
                throw new error_handler_1.ForensicsError(error_handler_1.ErrorCode.SECURITY_VIOLATION, 'API base URL must use HTTPS');
            }
        }
        if (config.requestTimeout !== undefined) {
            this.securityValidator.validateNumericInput(config.requestTimeout, 'requestTimeout', 1000, 300000);
        }
        if (config.rateLimitDelay !== undefined) {
            this.securityValidator.validateNumericInput(config.rateLimitDelay, 'rateLimitDelay', 0, 10000);
        }
        if (config.maxRetries !== undefined) {
            this.securityValidator.validateNumericInput(config.maxRetries, 'maxRetries', 0, 10);
        }
    }
}
exports.InputValidator = InputValidator;
InputValidator.securityValidator = new SecurityValidator();
/**
 * Security middleware for API requests
 */
class SecurityMiddleware {
    /**
     * Apply security checks to API requests
     */
    static async secureApiRequest(requestFn, identifier, context) {
        // Rate limiting check
        if (!this.validator.checkRateLimit(identifier)) {
            throw new error_handler_1.ForensicsError(error_handler_1.ErrorCode.API_RATE_LIMITED, 'Rate limit exceeded for API requests', {
                identifier,
                context,
            });
        }
        try {
            const result = await requestFn();
            return result;
        }
        catch (error) {
            logger_1.logger.warn('Secured API request failed', {
                identifier,
                error: error instanceof Error ? error.message : String(error),
                context,
            });
            throw error;
        }
    }
    /**
     * Periodic cleanup of security records
     */
    static startCleanupTimer() {
        return setInterval(() => {
            this.validator.cleanupRateLimitRecords();
        }, 5 * 60 * 1000); // Clean up every 5 minutes
    }
}
exports.SecurityMiddleware = SecurityMiddleware;
SecurityMiddleware.validator = new SecurityValidator();
/**
 * Export singleton instance
 */
exports.securityValidator = new SecurityValidator();
//# sourceMappingURL=security.js.map