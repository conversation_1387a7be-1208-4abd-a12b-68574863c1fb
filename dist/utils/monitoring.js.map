{"version": 3, "file": "monitoring.js", "sourceRoot": "", "sources": ["../../src/utils/monitoring.ts"], "names": [], "mappings": ";;;;;;AAAA,4CAAoB;AACpB,4CAAoB;AACpB,2CAAyC;AACzC,qCAAkC;AAClC,mDAA+C;AA8D/C,MAAa,iBAAiB;IAyB5B;QAtBQ,uBAAkB,GAAyB,EAAE,CAAC;QAC9C,yBAAoB,GAKxB;YACF,KAAK,EAAE,CAAC;YACR,UAAU,EAAE,CAAC;YACb,MAAM,EAAE,CAAC;YACT,aAAa,EAAE,CAAC;SACjB,CAAC;QACM,mBAAc,GAIlB;YACF,YAAY,EAAE,CAAC;YACf,UAAU,EAAE,CAAC;YACb,iBAAiB,EAAE,CAAC;SACrB,CAAC;QAGA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC5B,IAAI,CAAC,yBAAyB,EAAE,CAAC;IACnC,CAAC;IAEM,MAAM,CAAC,WAAW;QACvB,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,CAAC;YAChC,iBAAiB,CAAC,QAAQ,GAAG,IAAI,iBAAiB,EAAE,CAAC;QACvD,CAAC;QACD,OAAO,iBAAiB,CAAC,QAAQ,CAAC;IACpC,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,eAAe;QAC1B,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;QAC3C,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC;QAE3C,MAAM,MAAM,GAAG;YACb,MAAM,EAAE,MAAM,IAAI,CAAC,iBAAiB,EAAE;YACtC,IAAI,EAAE,MAAM,IAAI,CAAC,eAAe,EAAE;YAClC,GAAG,EAAE,MAAM,IAAI,CAAC,cAAc,EAAE;YAChC,MAAM,EAAE,MAAM,IAAI,CAAC,gBAAgB,EAAE;SACtC,CAAC;QAEF,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAE9C,2BAA2B;QAC3B,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,KAAK,MAAM,CAAC,CAAC,MAAM,CAAC;QAC3F,MAAM,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,KAAK,MAAM,CAAC,CAAC,MAAM,CAAC;QAEzF,IAAI,MAA4C,CAAC;QACjD,IAAI,YAAY,GAAG,CAAC,EAAE,CAAC;YACrB,MAAM,GAAG,WAAW,CAAC;QACvB,CAAC;aAAM,IAAI,UAAU,GAAG,CAAC,EAAE,CAAC;YAC1B,MAAM,GAAG,UAAU,CAAC;QACtB,CAAC;aAAM,CAAC;YACN,MAAM,GAAG,SAAS,CAAC;QACrB,CAAC;QAED,OAAO;YACL,MAAM;YACN,SAAS;YACT,MAAM;YACN,OAAO,EAAE,OAAO;YAChB,MAAM;YACN,OAAO;SACR,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,iBAAiB,CACtB,aAAqB,EACrB,QAAgB,EAChB,OAAgB,EAChB,QAA8B;QAE9B,MAAM,MAAM,GAAuB;YACjC,aAAa;YACb,QAAQ;YACR,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,OAAO;YACP,QAAQ;SACT,CAAC;QAEF,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAErC,8BAA8B;QAC9B,IAAI,IAAI,CAAC,kBAAkB,CAAC,MAAM,GAAG,IAAI,EAAE,CAAC;YAC1C,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC;QACjE,CAAC;QAED,sBAAsB;QACtB,IAAI,QAAQ,GAAG,KAAK,EAAE,CAAC;YACrB,aAAa;YACb,eAAM,CAAC,IAAI,CAAC,yBAAyB,EAAE;gBACrC,aAAa;gBACb,QAAQ;gBACR,OAAO;gBACP,QAAQ;aACT,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;OAEG;IACI,mBAAmB,CAAC,OAAgB,EAAE,QAAgB;QAC3D,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,CAAC;QAClC,IAAI,CAAC,oBAAoB,CAAC,aAAa,IAAI,QAAQ,CAAC;QAEpD,IAAI,OAAO,EAAE,CAAC;YACZ,IAAI,CAAC,oBAAoB,CAAC,UAAU,EAAE,CAAC;QACzC,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,CAAC;QACrC,CAAC;IACH,CAAC;IAED;;OAEG;IACI,oBAAoB,CAAC,OAAgB,EAAE,YAAoB;QAChE,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE,CAAC;QACnC,IAAI,CAAC,cAAc,CAAC,iBAAiB,IAAI,YAAY,CAAC;QAEtD,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,CAAC;QACnC,CAAC;IACH,CAAC;IAED;;OAEG;IACI,mBAAmB;QAOxB,IAAI,IAAI,CAAC,kBAAkB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACzC,OAAO;gBACL,eAAe,EAAE,CAAC;gBAClB,eAAe,EAAE,CAAC;gBAClB,WAAW,EAAE,CAAC;gBACd,cAAc,EAAE,CAAC;gBACjB,kBAAkB,EAAE,EAAE;aACvB,CAAC;QACJ,CAAC;QAED,MAAM,eAAe,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC;QACvD,MAAM,aAAa,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;QACtF,MAAM,oBAAoB,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC;QACnF,MAAM,cAAc,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,GAAG,KAAK,CAAC,CAAC,MAAM,CAAC;QAEtF,MAAM,kBAAkB,GAGpB,EAAE,CAAC;QAEP,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC7C,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,aAAa,CAAC,EAAE,CAAC;gBAC9C,kBAAkB,CAAC,MAAM,CAAC,aAAa,CAAC,GAAG,EAAE,KAAK,EAAE,CAAC,EAAE,WAAW,EAAE,CAAC,EAAE,WAAW,EAAE,CAAC,EAAE,CAAC;YAC1F,CAAC;YAED,MAAM,SAAS,GAAG,kBAAkB,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;YAC3D,SAAS,CAAC,KAAK,EAAE,CAAC;YAClB,SAAS,CAAC,WAAW;gBACnB,CAAC,SAAS,CAAC,WAAW,GAAG,CAAC,SAAS,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC,GAAG,SAAS,CAAC,KAAK,CAAC;YACtF,SAAS,CAAC,WAAW;gBACnB,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,aAAa,KAAK,MAAM,CAAC,aAAa,IAAI,CAAC,CAAC,OAAO,CAAC;qBACvF,MAAM,GAAG,SAAS,CAAC,KAAK,CAAC;QAChC,CAAC;QAED,OAAO;YACL,eAAe;YACf,eAAe,EAAE,aAAa,GAAG,eAAe;YAChD,WAAW,EAAE,oBAAoB,GAAG,eAAe;YACnD,cAAc;YACd,kBAAkB;SACnB,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,aAAa,CAAI,aAAqB,EAAE,QAA8B;QAC3E,OAAO,CAAC,MAAW,EAAE,YAAoB,EAAE,UAA8B,EAAE,EAAE;YAC3E,MAAM,MAAM,GAAG,UAAU,CAAC,KAAK,CAAC;YAEhC,UAAU,CAAC,KAAK,GAAG,KAAK,WAAW,GAAG,IAAW;gBAC/C,MAAM,SAAS,GAAG,wBAAW,CAAC,GAAG,EAAE,CAAC;gBACpC,IAAI,OAAO,GAAG,IAAI,CAAC;gBACnB,IAAI,MAAS,CAAC;gBAEd,IAAI,CAAC;oBACH,MAAM,GAAG,MAAM,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;oBACxC,OAAO,MAAM,CAAC;gBAChB,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,OAAO,GAAG,KAAK,CAAC;oBAChB,MAAM,KAAK,CAAC;gBACd,CAAC;wBAAS,CAAC;oBACT,MAAM,QAAQ,GAAG,wBAAW,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;oBAC/C,iBAAiB,CAAC,WAAW,EAAE,CAAC,iBAAiB,CAC/C,aAAa,EACb,QAAQ,EACR,OAAO,EACP,QAAQ,CACT,CAAC;gBACJ,CAAC;YACH,CAAC,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,iBAAiB;QAC7B,MAAM,QAAQ,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;QACvC,MAAM,QAAQ,GAAG,YAAE,CAAC,QAAQ,EAAE,CAAC;QAC/B,MAAM,OAAO,GAAG,YAAE,CAAC,OAAO,EAAE,CAAC;QAC7B,MAAM,OAAO,GAAG,QAAQ,GAAG,OAAO,CAAC;QACnC,MAAM,aAAa,GAAG,CAAC,OAAO,GAAG,QAAQ,CAAC,GAAG,GAAG,CAAC;QAEjD,MAAM,cAAc,GAAG,CAAC,QAAQ,CAAC,QAAQ,GAAG,QAAQ,CAAC,SAAS,CAAC,GAAG,GAAG,CAAC;QAEtE,IAAI,MAAM,GAA6B,MAAM,CAAC;QAC9C,IAAI,OAAO,GAAG,wBAAwB,CAAC;QAEvC,IAAI,aAAa,GAAG,EAAE,IAAI,cAAc,GAAG,EAAE,EAAE,CAAC;YAC9C,MAAM,GAAG,MAAM,CAAC;YAChB,OAAO,GAAG,gCAAgC,CAAC;QAC7C,CAAC;aAAM,IAAI,aAAa,GAAG,EAAE,IAAI,cAAc,GAAG,EAAE,EAAE,CAAC;YACrD,MAAM,GAAG,MAAM,CAAC;YAChB,OAAO,GAAG,4BAA4B,CAAC;QACzC,CAAC;QAED,OAAO;YACL,MAAM;YACN,OAAO;YACP,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE,cAAc,CAAC;YAC9C,SAAS,EAAE,EAAE;YACb,WAAW,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACtC,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,eAAe;QAC3B,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,YAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC;YACzC,oFAAoF;YAEpF,OAAO;gBACL,MAAM,EAAE,MAAM;gBACd,OAAO,EAAE,uBAAuB;gBAChC,WAAW,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACtC,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,MAAM,EAAE,MAAM;gBACd,OAAO,EAAE,4BAA4B;gBACrC,WAAW,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACtC,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,cAAc;QAC1B,MAAM,SAAS,GACb,IAAI,CAAC,cAAc,CAAC,YAAY,GAAG,CAAC;YAClC,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,GAAG,GAAG;YAC3E,CAAC,CAAC,CAAC,CAAC;QAER,MAAM,eAAe,GACnB,IAAI,CAAC,cAAc,CAAC,YAAY,GAAG,CAAC;YAClC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,iBAAiB,GAAG,IAAI,CAAC,cAAc,CAAC,YAAY;YAC1E,CAAC,CAAC,CAAC,CAAC;QAER,IAAI,MAAM,GAA6B,MAAM,CAAC;QAC9C,IAAI,OAAO,GAAG,2BAA2B,CAAC;QAE1C,IAAI,SAAS,GAAG,EAAE,IAAI,eAAe,GAAG,KAAK,EAAE,CAAC;YAC9C,MAAM,GAAG,MAAM,CAAC;YAChB,OAAO,GAAG,0CAA0C,CAAC;QACvD,CAAC;aAAM,IAAI,SAAS,GAAG,EAAE,IAAI,eAAe,GAAG,KAAK,EAAE,CAAC;YACrD,MAAM,GAAG,MAAM,CAAC;YAChB,OAAO,GAAG,sCAAsC,CAAC;QACnD,CAAC;QAED,OAAO;YACL,MAAM;YACN,OAAO;YACP,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,eAAe,GAAG,IAAI,CAAC;YAClD,SAAS,EAAE,EAAE;YACb,WAAW,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACtC,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,gBAAgB;QAC5B,MAAM,YAAY,GAAG,4BAAY,CAAC,WAAW,EAAE,CAAC;QAChD,MAAM,UAAU,GAAG,YAAY,CAAC,aAAa,EAAE,CAAC;QAEhD,MAAM,gBAAgB,GAAG,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE;YACjF,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE,CAAC;YAChD,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;YAC/C,OAAO,SAAS,GAAG,UAAU,CAAC;QAChC,CAAC,CAAC,CAAC,MAAM,CAAC;QAEV,IAAI,MAAM,GAA6B,MAAM,CAAC;QAC9C,IAAI,OAAO,GAAG,sBAAsB,CAAC;QAErC,IAAI,gBAAgB,GAAG,EAAE,EAAE,CAAC;YAC1B,MAAM,GAAG,MAAM,CAAC;YAChB,OAAO,GAAG,8BAA8B,CAAC;QAC3C,CAAC;aAAM,IAAI,gBAAgB,GAAG,EAAE,EAAE,CAAC;YACjC,MAAM,GAAG,MAAM,CAAC;YAChB,OAAO,GAAG,8BAA8B,CAAC;QAC3C,CAAC;QAED,OAAO;YACL,MAAM;YACN,OAAO;YACP,KAAK,EAAE,gBAAgB;YACvB,SAAS,EAAE,EAAE;YACb,WAAW,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACtC,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,gBAAgB;QAC5B,MAAM,QAAQ,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;QACvC,MAAM,QAAQ,GAAG,YAAE,CAAC,QAAQ,EAAE,CAAC;QAC/B,MAAM,OAAO,GAAG,YAAE,CAAC,OAAO,EAAE,CAAC;QAC7B,MAAM,OAAO,GAAG,QAAQ,GAAG,OAAO,CAAC;QAEnC,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,EAAE,CAAC;QACpC,MAAM,WAAW,GAAG,YAAE,CAAC,OAAO,EAAE,CAAC;QAEjC,OAAO;YACL,MAAM,EAAE;gBACN,IAAI,EAAE,OAAO;gBACb,KAAK,EAAE,QAAQ;gBACf,UAAU,EAAE,CAAC,OAAO,GAAG,QAAQ,CAAC,GAAG,GAAG;gBACtC,QAAQ,EAAE,QAAQ,CAAC,QAAQ;gBAC3B,SAAS,EAAE,QAAQ,CAAC,SAAS;aAC9B;YACD,GAAG,EAAE;gBACH,KAAK,EAAE,CAAC,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC,MAAM,CAAC,GAAG,OAAO,EAAE,qBAAqB;gBACzE,WAAW;aACZ;YACD,IAAI,EAAE;gBACJ,IAAI,EAAE,CAAC,EAAE,0CAA0C;gBACnD,SAAS,EAAE,CAAC;gBACZ,UAAU,EAAE,CAAC;aACd;YACD,OAAO,EAAE;gBACP,YAAY,EAAE,IAAI,CAAC,cAAc,CAAC,YAAY;gBAC9C,UAAU,EAAE,IAAI,CAAC,cAAc,CAAC,UAAU;gBAC1C,mBAAmB,EACjB,IAAI,CAAC,cAAc,CAAC,YAAY,GAAG,CAAC;oBAClC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,iBAAiB,GAAG,IAAI,CAAC,cAAc,CAAC,YAAY;oBAC1E,CAAC,CAAC,CAAC;aACR;YACD,cAAc,EAAE;gBACd,KAAK,EAAE,IAAI,CAAC,oBAAoB,CAAC,KAAK;gBACtC,UAAU,EAAE,IAAI,CAAC,oBAAoB,CAAC,UAAU;gBAChD,MAAM,EAAE,IAAI,CAAC,oBAAoB,CAAC,MAAM;gBACxC,eAAe,EACb,IAAI,CAAC,oBAAoB,CAAC,KAAK,GAAG,CAAC;oBACjC,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,aAAa,GAAG,IAAI,CAAC,oBAAoB,CAAC,KAAK;oBAC3E,CAAC,CAAC,CAAC;aACR;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,yBAAyB;QAC/B,oCAAoC;QACpC,WAAW,CACT,KAAK,IAAI,EAAE;YACT,IAAI,CAAC;gBACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;gBAE5C,IAAI,MAAM,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;oBAChC,eAAM,CAAC,IAAI,CAAC,qBAAqB,EAAE;wBACjC,MAAM,EAAE,MAAM,CAAC,MAAM;wBACrB,YAAY,EAAE,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC;6BACxC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,KAAK,MAAM,CAAC;6BAC/C,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;qBAC9D,CAAC,CAAC;gBACL,CAAC;qBAAM,CAAC;oBACN,eAAM,CAAC,IAAI,CAAC,4BAA4B,EAAE;wBACxC,MAAM,EAAE,MAAM,CAAC,MAAM;wBACrB,WAAW,EAAE,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,UAAU;qBAC9C,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE;oBAClC,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;iBAC9D,CAAC,CAAC;YACL,CAAC;QACH,CAAC,EACD,CAAC,GAAG,EAAE,GAAG,IAAI,CACd,CAAC,CAAC,YAAY;QAEf,8CAA8C;QAC9C,WAAW,CACT,GAAG,EAAE;YACH,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;YAC/C,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,CACtD,MAAM,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE,GAAG,UAAU,CAC5D,CAAC;QACJ,CAAC,EACD,EAAE,GAAG,EAAE,GAAG,IAAI,CACf,CAAC,CAAC,SAAS;IACd,CAAC;CACF;AAnbD,8CAmbC;AAED;;GAEG;AACU,QAAA,UAAU,GAAG,iBAAiB,CAAC,WAAW,EAAE,CAAC"}