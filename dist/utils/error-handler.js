"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ErrorUtils = exports.ErrorHandler = exports.ForensicsError = exports.ErrorCode = void 0;
exports.setupGlobalErrorHandling = setupGlobalErrorHandling;
const logger_1 = require("./logger");
var ErrorCode;
(function (ErrorCode) {
    // Validation errors
    ErrorCode["INVALID_BITCOIN_ADDRESS"] = "INVALID_BITCOIN_ADDRESS";
    ErrorCode["INVALID_TRANSACTION_ID"] = "INVALID_TRANSACTION_ID";
    ErrorCode["INVALID_INPUT_PARAMETERS"] = "INVALID_INPUT_PARAMETERS";
    // API errors
    ErrorCode["API_REQUEST_FAILED"] = "API_REQUEST_FAILED";
    ErrorCode["API_RATE_LIMITED"] = "API_RATE_LIMITED";
    ErrorCode["API_TIMEOUT"] = "API_TIMEOUT";
    ErrorCode["API_UNAUTHORIZED"] = "API_UNAUTHORIZED";
    // Investigation errors
    ErrorCode["INVESTIGATION_FAILED"] = "INVESTIGATION_FAILED";
    ErrorCode["TRANSACTION_NOT_FOUND"] = "TRANSACTION_NOT_FOUND";
    ErrorCode["ADDRESS_NOT_FOUND"] = "ADDRESS_NOT_FOUND";
    ErrorCode["INSUFFICIENT_DATA"] = "INSUFFICIENT_DATA";
    // File system errors
    ErrorCode["FILE_NOT_FOUND"] = "FILE_NOT_FOUND";
    ErrorCode["FILE_WRITE_ERROR"] = "FILE_WRITE_ERROR";
    ErrorCode["FILE_READ_ERROR"] = "FILE_READ_ERROR";
    ErrorCode["DIRECTORY_CREATE_ERROR"] = "DIRECTORY_CREATE_ERROR";
    // Security errors
    ErrorCode["SECURITY_VIOLATION"] = "SECURITY_VIOLATION";
    ErrorCode["UNAUTHORIZED_ACCESS"] = "UNAUTHORIZED_ACCESS";
    ErrorCode["DATA_INTEGRITY_ERROR"] = "DATA_INTEGRITY_ERROR";
    // System errors
    ErrorCode["MEMORY_LIMIT_EXCEEDED"] = "MEMORY_LIMIT_EXCEEDED";
    ErrorCode["TIMEOUT_ERROR"] = "TIMEOUT_ERROR";
    ErrorCode["CONFIGURATION_ERROR"] = "CONFIGURATION_ERROR";
    ErrorCode["UNKNOWN_ERROR"] = "UNKNOWN_ERROR";
})(ErrorCode || (exports.ErrorCode = ErrorCode = {}));
class ForensicsError extends Error {
    constructor(code, message, details, userFriendlyMessage, isOperational = true) {
        super(message);
        this.name = 'ForensicsError';
        this.code = code;
        this.isOperational = isOperational;
        this.context = {
            code,
            message,
            details,
            timestamp: new Date().toISOString(),
            stack: this.stack,
            userFriendlyMessage: userFriendlyMessage || this.getDefaultUserMessage(code),
            recoverable: this.isRecoverable(code),
            retryable: this.isRetryable(code),
        };
        // Capture stack trace
        Error.captureStackTrace(this, ForensicsError);
    }
    getDefaultUserMessage(code) {
        const userMessages = {
            [ErrorCode.INVALID_BITCOIN_ADDRESS]: 'The Bitcoin address you provided is not valid. Please check and try again.',
            [ErrorCode.INVALID_TRANSACTION_ID]: 'The transaction ID you provided is not valid. Please check and try again.',
            [ErrorCode.INVALID_INPUT_PARAMETERS]: 'Some of the information you provided is not valid. Please check your inputs.',
            [ErrorCode.API_REQUEST_FAILED]: 'We encountered an issue connecting to the Bitcoin network. Please try again later.',
            [ErrorCode.API_RATE_LIMITED]: 'We are making too many requests. Please wait a moment and try again.',
            [ErrorCode.API_TIMEOUT]: 'The request took too long to complete. Please try again.',
            [ErrorCode.API_UNAUTHORIZED]: 'Access to the Bitcoin network was denied. Please check your configuration.',
            [ErrorCode.INVESTIGATION_FAILED]: 'The investigation could not be completed. Please try again or contact support.',
            [ErrorCode.TRANSACTION_NOT_FOUND]: 'The transaction could not be found on the Bitcoin network.',
            [ErrorCode.ADDRESS_NOT_FOUND]: 'The Bitcoin address could not be found or has no transaction history.',
            [ErrorCode.INSUFFICIENT_DATA]: 'Not enough data was found to complete the investigation.',
            [ErrorCode.FILE_NOT_FOUND]: 'A required file could not be found.',
            [ErrorCode.FILE_WRITE_ERROR]: 'Could not save the investigation results. Please check file permissions.',
            [ErrorCode.FILE_READ_ERROR]: 'Could not read a required file. Please check file permissions.',
            [ErrorCode.DIRECTORY_CREATE_ERROR]: 'Could not create the output directory. Please check permissions.',
            [ErrorCode.SECURITY_VIOLATION]: 'A security issue was detected. The operation has been blocked.',
            [ErrorCode.UNAUTHORIZED_ACCESS]: 'You do not have permission to perform this operation.',
            [ErrorCode.DATA_INTEGRITY_ERROR]: 'Data integrity check failed. The data may have been corrupted.',
            [ErrorCode.MEMORY_LIMIT_EXCEEDED]: 'The investigation requires too much memory. Please try with smaller parameters.',
            [ErrorCode.TIMEOUT_ERROR]: 'The operation took too long to complete. Please try again.',
            [ErrorCode.CONFIGURATION_ERROR]: 'There is an issue with the tool configuration. Please check your settings.',
            [ErrorCode.UNKNOWN_ERROR]: 'An unexpected error occurred. Please try again or contact support.',
        };
        return (userMessages[code] || 'An unexpected error occurred. Please try again or contact support.');
    }
    isRecoverable(code) {
        const recoverableErrors = [
            ErrorCode.API_RATE_LIMITED,
            ErrorCode.API_TIMEOUT,
            ErrorCode.FILE_WRITE_ERROR,
            ErrorCode.DIRECTORY_CREATE_ERROR,
            ErrorCode.TIMEOUT_ERROR,
        ];
        return recoverableErrors.includes(code);
    }
    isRetryable(code) {
        const retryableErrors = [
            ErrorCode.API_REQUEST_FAILED,
            ErrorCode.API_RATE_LIMITED,
            ErrorCode.API_TIMEOUT,
            ErrorCode.TIMEOUT_ERROR,
        ];
        return retryableErrors.includes(code);
    }
}
exports.ForensicsError = ForensicsError;
class ErrorHandler {
    constructor() {
        this.errorCounts = new Map();
        this.lastErrors = new Map();
    }
    static getInstance() {
        if (!ErrorHandler.instance) {
            ErrorHandler.instance = new ErrorHandler();
        }
        return ErrorHandler.instance;
    }
    /**
     * Handle and log errors with appropriate context
     */
    handleError(error, context) {
        let errorContext;
        if (error instanceof ForensicsError) {
            errorContext = error.context;
        }
        else {
            // Convert regular errors to ForensicsError
            const forensicsError = this.convertToForensicsError(error);
            errorContext = forensicsError.context;
        }
        // Add additional context
        if (context) {
            errorContext.details = { ...errorContext.details, ...context };
        }
        // Track error frequency
        this.trackError(errorContext.code);
        // Log the error
        this.logError(errorContext);
        return errorContext;
    }
    /**
     * Convert regular errors to ForensicsError
     */
    convertToForensicsError(error) {
        let code = ErrorCode.UNKNOWN_ERROR;
        let isOperational = false;
        // Try to determine error type from message or properties
        if (error.message.includes('ENOENT') || error.message.includes('file not found')) {
            code = ErrorCode.FILE_NOT_FOUND;
            isOperational = true;
        }
        else if (error.message.includes('EACCES') || error.message.includes('permission denied')) {
            code = ErrorCode.UNAUTHORIZED_ACCESS;
            isOperational = true;
        }
        else if (error.message.includes('timeout')) {
            code = ErrorCode.TIMEOUT_ERROR;
            isOperational = true;
        }
        else if (error.message.includes('network') || error.message.includes('ECONNREFUSED')) {
            code = ErrorCode.API_REQUEST_FAILED;
            isOperational = true;
        }
        return new ForensicsError(code, error.message, { originalError: error.name, stack: error.stack }, undefined, isOperational);
    }
    /**
     * Track error frequency for monitoring
     */
    trackError(code) {
        const count = this.errorCounts.get(code) || 0;
        this.errorCounts.set(code, count + 1);
        this.lastErrors.set(code, new Date());
    }
    /**
     * Log error with appropriate level
     */
    logError(errorContext) {
        const logData = {
            code: errorContext.code,
            message: errorContext.message,
            details: errorContext.details,
            timestamp: errorContext.timestamp,
            recoverable: errorContext.recoverable,
            retryable: errorContext.retryable,
        };
        if (errorContext.recoverable) {
            logger_1.logger.warn('Recoverable error occurred', logData);
        }
        else {
            logger_1.logger.error('Error occurred', logData);
        }
    }
    /**
     * Get error statistics for monitoring
     */
    getErrorStats() {
        const totalErrors = Array.from(this.errorCounts.values()).reduce((sum, count) => sum + count, 0);
        const errorsByCode = {};
        this.errorCounts.forEach((count, code) => {
            errorsByCode[code] = count;
        });
        const recentErrors = {};
        this.lastErrors.forEach((date, code) => {
            recentErrors[code] = date.toISOString();
        });
        return {
            totalErrors,
            errorsByCode,
            recentErrors,
        };
    }
    /**
     * Reset error statistics
     */
    resetStats() {
        this.errorCounts.clear();
        this.lastErrors.clear();
    }
    /**
     * Check if an error should trigger an alert
     */
    shouldAlert(code) {
        const count = this.errorCounts.get(code) || 0;
        const lastError = this.lastErrors.get(code);
        // Alert thresholds
        const ALERT_THRESHOLDS = {
            [ErrorCode.API_REQUEST_FAILED]: 5,
            [ErrorCode.SECURITY_VIOLATION]: 1,
            [ErrorCode.DATA_INTEGRITY_ERROR]: 1,
            [ErrorCode.MEMORY_LIMIT_EXCEEDED]: 3,
            [ErrorCode.UNKNOWN_ERROR]: 10,
        };
        const threshold = ALERT_THRESHOLDS[code] || 20;
        // Alert if we've exceeded the threshold in the last hour
        if (count >= threshold && lastError) {
            const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
            return lastError > oneHourAgo;
        }
        return false;
    }
}
exports.ErrorHandler = ErrorHandler;
/**
 * Global error handler for uncaught exceptions
 */
function setupGlobalErrorHandling() {
    const errorHandler = ErrorHandler.getInstance();
    process.on('uncaughtException', (error) => {
        const context = errorHandler.handleError(error, { source: 'uncaughtException' });
        console.error('Uncaught Exception:', context.userFriendlyMessage);
        // Exit gracefully for non-operational errors
        if (!context.recoverable) {
            process.exit(1);
        }
    });
    process.on('unhandledRejection', (reason, promise) => {
        const error = reason instanceof Error ? reason : new Error(String(reason));
        const context = errorHandler.handleError(error, {
            source: 'unhandledRejection',
            promise: promise.toString(),
        });
        console.error('Unhandled Rejection:', context.userFriendlyMessage);
        // Exit gracefully for non-operational errors
        if (!context.recoverable) {
            process.exit(1);
        }
    });
}
/**
 * Utility functions for common error scenarios
 */
exports.ErrorUtils = {
    /**
     * Create a validation error
     */
    validation: (message, details) => new ForensicsError(ErrorCode.INVALID_INPUT_PARAMETERS, message, details),
    /**
     * Create an API error
     */
    api: (message, details) => new ForensicsError(ErrorCode.API_REQUEST_FAILED, message, details),
    /**
     * Create a file system error
     */
    fileSystem: (message, details) => new ForensicsError(ErrorCode.FILE_WRITE_ERROR, message, details),
    /**
     * Create a security error
     */
    security: (message, details) => new ForensicsError(ErrorCode.SECURITY_VIOLATION, message, details),
    /**
     * Wrap async functions with error handling
     */
    wrapAsync: (fn, errorCode = ErrorCode.UNKNOWN_ERROR) => {
        return async (...args) => {
            try {
                return await fn(...args);
            }
            catch (error) {
                if (error instanceof ForensicsError) {
                    throw error;
                }
                throw new ForensicsError(errorCode, error instanceof Error ? error.message : String(error), { originalError: error });
            }
        };
    },
};
//# sourceMappingURL=error-handler.js.map