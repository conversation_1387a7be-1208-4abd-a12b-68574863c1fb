"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.monitoring = exports.MonitoringService = void 0;
const os_1 = __importDefault(require("os"));
const fs_1 = __importDefault(require("fs"));
const perf_hooks_1 = require("perf_hooks");
const logger_1 = require("./logger");
const error_handler_1 = require("./error-handler");
class MonitoringService {
    constructor() {
        this.performanceMetrics = [];
        this.investigationMetrics = {
            total: 0,
            successful: 0,
            failed: 0,
            totalDuration: 0,
        };
        this.networkMetrics = {
            requestCount: 0,
            errorCount: 0,
            totalResponseTime: 0,
        };
        this.startTime = Date.now();
        this.startPeriodicHealthChecks();
    }
    static getInstance() {
        if (!MonitoringService.instance) {
            MonitoringService.instance = new MonitoringService();
        }
        return MonitoringService.instance;
    }
    /**
     * Get comprehensive health status
     */
    async getHealthStatus() {
        const timestamp = new Date().toISOString();
        const uptime = Date.now() - this.startTime;
        const checks = {
            memory: await this.checkMemoryHealth(),
            disk: await this.checkDiskHealth(),
            api: await this.checkApiHealth(),
            errors: await this.checkErrorHealth(),
        };
        const metrics = await this.getSystemMetrics();
        // Determine overall status
        const failedChecks = Object.values(checks).filter(check => check.status === 'fail').length;
        const warnChecks = Object.values(checks).filter(check => check.status === 'warn').length;
        let status;
        if (failedChecks > 0) {
            status = 'unhealthy';
        }
        else if (warnChecks > 0) {
            status = 'degraded';
        }
        else {
            status = 'healthy';
        }
        return {
            status,
            timestamp,
            uptime,
            version: '3.0.0',
            checks,
            metrics,
        };
    }
    /**
     * Record performance metrics
     */
    recordPerformance(operationName, duration, success, metadata) {
        const metric = {
            operationName,
            duration,
            timestamp: new Date().toISOString(),
            success,
            metadata,
        };
        this.performanceMetrics.push(metric);
        // Keep only last 1000 metrics
        if (this.performanceMetrics.length > 1000) {
            this.performanceMetrics = this.performanceMetrics.slice(-1000);
        }
        // Log slow operations
        if (duration > 10000) {
            // 10 seconds
            logger_1.logger.warn('Slow operation detected', {
                operationName,
                duration,
                success,
                metadata,
            });
        }
    }
    /**
     * Record investigation metrics
     */
    recordInvestigation(success, duration) {
        this.investigationMetrics.total++;
        this.investigationMetrics.totalDuration += duration;
        if (success) {
            this.investigationMetrics.successful++;
        }
        else {
            this.investigationMetrics.failed++;
        }
    }
    /**
     * Record network request metrics
     */
    recordNetworkRequest(success, responseTime) {
        this.networkMetrics.requestCount++;
        this.networkMetrics.totalResponseTime += responseTime;
        if (!success) {
            this.networkMetrics.errorCount++;
        }
    }
    /**
     * Get performance statistics
     */
    getPerformanceStats() {
        if (this.performanceMetrics.length === 0) {
            return {
                totalOperations: 0,
                averageDuration: 0,
                successRate: 0,
                slowOperations: 0,
                operationBreakdown: {},
            };
        }
        const totalOperations = this.performanceMetrics.length;
        const totalDuration = this.performanceMetrics.reduce((sum, m) => sum + m.duration, 0);
        const successfulOperations = this.performanceMetrics.filter(m => m.success).length;
        const slowOperations = this.performanceMetrics.filter(m => m.duration > 10000).length;
        const operationBreakdown = {};
        for (const metric of this.performanceMetrics) {
            if (!operationBreakdown[metric.operationName]) {
                operationBreakdown[metric.operationName] = { count: 0, avgDuration: 0, successRate: 0 };
            }
            const breakdown = operationBreakdown[metric.operationName];
            breakdown.count++;
            breakdown.avgDuration =
                (breakdown.avgDuration * (breakdown.count - 1) + metric.duration) / breakdown.count;
            breakdown.successRate =
                this.performanceMetrics.filter(m => m.operationName === metric.operationName && m.success)
                    .length / breakdown.count;
        }
        return {
            totalOperations,
            averageDuration: totalDuration / totalOperations,
            successRate: successfulOperations / totalOperations,
            slowOperations,
            operationBreakdown,
        };
    }
    /**
     * Performance timing decorator
     */
    timeOperation(operationName, metadata) {
        return (target, propertyName, descriptor) => {
            const method = descriptor.value;
            descriptor.value = async function (...args) {
                const startTime = perf_hooks_1.performance.now();
                let success = true;
                let result;
                try {
                    result = await method.apply(this, args);
                    return result;
                }
                catch (error) {
                    success = false;
                    throw error;
                }
                finally {
                    const duration = perf_hooks_1.performance.now() - startTime;
                    MonitoringService.getInstance().recordPerformance(operationName, duration, success, metadata);
                }
            };
        };
    }
    /**
     * Check memory health
     */
    async checkMemoryHealth() {
        const memUsage = process.memoryUsage();
        const totalMem = os_1.default.totalmem();
        const freeMem = os_1.default.freemem();
        const usedMem = totalMem - freeMem;
        const memPercentage = (usedMem / totalMem) * 100;
        const heapPercentage = (memUsage.heapUsed / memUsage.heapTotal) * 100;
        let status = 'pass';
        let message = 'Memory usage is normal';
        if (memPercentage > 90 || heapPercentage > 90) {
            status = 'fail';
            message = 'Critical memory usage detected';
        }
        else if (memPercentage > 80 || heapPercentage > 80) {
            status = 'warn';
            message = 'High memory usage detected';
        }
        return {
            status,
            message,
            value: Math.max(memPercentage, heapPercentage),
            threshold: 80,
            lastChecked: new Date().toISOString(),
        };
    }
    /**
     * Check disk health
     */
    async checkDiskHealth() {
        try {
            const stats = fs_1.default.statSync(process.cwd());
            // This is a simplified check - in production, you'd want to check actual disk usage
            return {
                status: 'pass',
                message: 'Disk access is normal',
                lastChecked: new Date().toISOString(),
            };
        }
        catch (error) {
            return {
                status: 'fail',
                message: 'Disk access error detected',
                lastChecked: new Date().toISOString(),
            };
        }
    }
    /**
     * Check API health
     */
    async checkApiHealth() {
        const errorRate = this.networkMetrics.requestCount > 0
            ? (this.networkMetrics.errorCount / this.networkMetrics.requestCount) * 100
            : 0;
        const avgResponseTime = this.networkMetrics.requestCount > 0
            ? this.networkMetrics.totalResponseTime / this.networkMetrics.requestCount
            : 0;
        let status = 'pass';
        let message = 'API performance is normal';
        if (errorRate > 50 || avgResponseTime > 30000) {
            status = 'fail';
            message = 'Critical API performance issues detected';
        }
        else if (errorRate > 20 || avgResponseTime > 10000) {
            status = 'warn';
            message = 'API performance degradation detected';
        }
        return {
            status,
            message,
            value: Math.max(errorRate, avgResponseTime / 1000),
            threshold: 20,
            lastChecked: new Date().toISOString(),
        };
    }
    /**
     * Check error health
     */
    async checkErrorHealth() {
        const errorHandler = error_handler_1.ErrorHandler.getInstance();
        const errorStats = errorHandler.getErrorStats();
        const recentErrorCount = Object.values(errorStats.recentErrors).filter(timestamp => {
            const errorTime = new Date(timestamp).getTime();
            const oneHourAgo = Date.now() - 60 * 60 * 1000;
            return errorTime > oneHourAgo;
        }).length;
        let status = 'pass';
        let message = 'Error rate is normal';
        if (recentErrorCount > 50) {
            status = 'fail';
            message = 'Critical error rate detected';
        }
        else if (recentErrorCount > 20) {
            status = 'warn';
            message = 'Elevated error rate detected';
        }
        return {
            status,
            message,
            value: recentErrorCount,
            threshold: 20,
            lastChecked: new Date().toISOString(),
        };
    }
    /**
     * Get system metrics
     */
    async getSystemMetrics() {
        const memUsage = process.memoryUsage();
        const totalMem = os_1.default.totalmem();
        const freeMem = os_1.default.freemem();
        const usedMem = totalMem - freeMem;
        const cpuUsage = process.cpuUsage();
        const loadAverage = os_1.default.loadavg();
        return {
            memory: {
                used: usedMem,
                total: totalMem,
                percentage: (usedMem / totalMem) * 100,
                heapUsed: memUsage.heapUsed,
                heapTotal: memUsage.heapTotal,
            },
            cpu: {
                usage: (cpuUsage.user + cpuUsage.system) / 1000000, // Convert to seconds
                loadAverage,
            },
            disk: {
                used: 0, // Would implement actual disk usage check
                available: 0,
                percentage: 0,
            },
            network: {
                requestCount: this.networkMetrics.requestCount,
                errorCount: this.networkMetrics.errorCount,
                averageResponseTime: this.networkMetrics.requestCount > 0
                    ? this.networkMetrics.totalResponseTime / this.networkMetrics.requestCount
                    : 0,
            },
            investigations: {
                total: this.investigationMetrics.total,
                successful: this.investigationMetrics.successful,
                failed: this.investigationMetrics.failed,
                averageDuration: this.investigationMetrics.total > 0
                    ? this.investigationMetrics.totalDuration / this.investigationMetrics.total
                    : 0,
            },
        };
    }
    /**
     * Start periodic health checks
     */
    startPeriodicHealthChecks() {
        // Log health status every 5 minutes
        setInterval(async () => {
            try {
                const health = await this.getHealthStatus();
                if (health.status !== 'healthy') {
                    logger_1.logger.warn('System health check', {
                        status: health.status,
                        failedChecks: Object.entries(health.checks)
                            .filter(([_, check]) => check.status === 'fail')
                            .map(([name, check]) => ({ name, message: check.message })),
                    });
                }
                else {
                    logger_1.logger.info('System health check passed', {
                        uptime: health.uptime,
                        memoryUsage: health.metrics.memory.percentage,
                    });
                }
            }
            catch (error) {
                logger_1.logger.error('Health check failed', {
                    error: error instanceof Error ? error.message : String(error),
                });
            }
        }, 5 * 60 * 1000); // 5 minutes
        // Clean up old performance metrics every hour
        setInterval(() => {
            const oneHourAgo = Date.now() - 60 * 60 * 1000;
            this.performanceMetrics = this.performanceMetrics.filter(metric => new Date(metric.timestamp).getTime() > oneHourAgo);
        }, 60 * 60 * 1000); // 1 hour
    }
}
exports.MonitoringService = MonitoringService;
/**
 * Export singleton instance
 */
exports.monitoring = MonitoringService.getInstance();
//# sourceMappingURL=monitoring.js.map