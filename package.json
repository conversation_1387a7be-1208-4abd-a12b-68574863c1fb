{"name": "bitcoin-forensic-investigator", "version": "3.0.0", "description": "Comprehensive Bitcoin forensic investigation tool for scam victims and professionals", "main": "dist/index.js", "bin": {"btc-forensics": "./dist/index.js", "bitcoin-investigator": "./dist/index.js"}, "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node src/index.ts", "watch": "tsc --watch", "clean": "<PERSON><PERSON><PERSON> dist", "test": "jest", "lint": "eslint src/**/*.ts", "format": "prettier --write src/**/*.ts", "prepare": "npm run build"}, "keywords": ["bitcoin", "cryptocurrency", "forensics", "investigation", "blockchain", "scam", "tracing", "cli"], "author": "Bitcoin Forensics Team", "license": "MIT", "dependencies": {"axios": "^1.6.0", "chalk": "^4.1.2", "cli-progress": "^3.12.0", "commander": "^11.1.0", "date-fns": "^2.30.0", "inquirer": "^8.2.6", "ora": "^5.4.1", "pdf-lib": "^1.17.1", "uuid": "^9.0.1", "winston": "^3.11.0"}, "devDependencies": {"@types/cli-progress": "^3.11.0", "@types/inquirer": "^9.0.3", "@types/jest": "^29.5.5", "@types/lodash": "^4.14.199", "@types/node": "^20.8.0", "@types/uuid": "^9.0.5", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "eslint": "^8.57.1", "jest": "^29.7.0", "prettier": "^3.0.3", "rimraf": "^5.0.5", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "typescript": "^5.2.2"}, "engines": {"node": ">=16.0.0"}, "repository": {"type": "git", "url": "https://github.com/bitcoin-forensics/investigator.git"}, "bugs": {"url": "https://github.com/bitcoin-forensics/investigator/issues"}, "homepage": "https://github.com/bitcoin-forensics/investigator#readme"}